# 这是服务器端配置文件
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    server {
		 listen 443 ssl;
		 server_name www.gs1helper.com; #填写绑定证书的域名
		 ssl_certificate   cert/www.gs1helper.com.pem;
		 ssl_certificate_key  cert/www.gs1helper.com.key;
		 ssl_session_timeout 5m;
		 client_max_body_size 80m;
		 ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
		 ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
		 ssl_prefer_server_ciphers on;

         location / {
            proxy_pass http://127.0.0.1:8888;
         }
	}

    server {
        listen 443 ssl;
        server_name api.gs1helper.com; # 微信后台使用

        # 转发websocket需要的设置
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
        proxy_set_header X_Forward_For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_read_timeout 600s;

        ssl_certificate cert/api.gs1helper.com.pem;
        ssl_certificate_key cert/api.gs1helper.com.key;
        ssl_session_timeout 5m;
        client_max_body_size 80m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_prefer_server_ciphers on;
        proxy_set_header X-Real-IP $remote_addr;
        location / {
            proxy_pass http://127.0.0.1:8080;
        }
    }

    server {
        listen 443 ssl;
        server_name qr.gs1helper.com; # 二维码

        # 转发websocket需要的设置
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
        proxy_set_header X_Forward_For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_read_timeout 600s;

        ssl_certificate cert/qr.gs1helper.com.pem;
        ssl_certificate_key cert/qr.gs1helper.com.key;
        ssl_session_timeout 5m;
        client_max_body_size 80m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_prefer_server_ciphers on;
        proxy_set_header X-Real-IP $remote_addr;
        location / {
            proxy_pass http://127.0.0.1:8889;
        }
    }

    server {
         listen 443 ssl;
         server_name wx.gs1helper.com; # 微信跳转使用
         ssl_certificate cert/wx.gs1helper.com.pem;
         ssl_certificate_key cert/wx.gs1helper.com.key;
         ssl_session_timeout 5m;
         client_max_body_size 80m;
         ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
         ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
         ssl_prefer_server_ciphers on;

        # 根目录和默认索引页
        root   html/wx.gs1helper.com;  # 修改为您的站点目录
        index  index.html index.htm;

        location ~* ^/([\w-]+)$ {
            return 301 https://wx.gs1helper.com/index.html?num=$1;
        }
    }

    server {
        listen 80;
        server_name www.gs1helper.com;
        rewrite ^(.*)$ https://${server_name}$1 permanent;
    }

}
