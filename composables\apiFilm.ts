// 参数接口
import type { PayState } from '~/composables/enum'
import { useFetchApi } from '~/composables/api'

export interface OrderPageParams {
  groupBy?: string
  isInvoiced?: number
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirection
  pageIndex?: number
  pageSize?: number
  payState?: PayState
  serverType?: number
  userId: number
}

export interface OrderPageResData {
  actuallyPrice: number
  createdDate: Record<string, unknown>
  endBarCode: string
  invoiceId: number
  invoiceState: number
  invoiceStateStr: string
  number: number
  orderCode: string
  orderContent: string
  orderId: number
  payDate: Record<string, unknown>
  payState: number
  payStateStr: string
  price: number
  serverType: number
  size: number
  startBarCode: string
  totalPrice: number
  transactionNo: string
  userId: number
}

// 响应接口
export interface OrderPageRes {
  data: OrderPageResData[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
export const orderPageApi = (params: OrderPageParams, options?: any) =>
  useFetchApi<OrderPageRes>('/api/orderPage', {
    body: params,
    ...options,
  })

export const downloadCodeZipPath = '/order/downloadCodeZip' // 订单条码下载

// 参数接口
export interface DownloadByOrderParams {
  downloadType: number
  email?: string
  orderId: number
  userId: number
}

// 响应接口
export interface DownloadByOrderRes {
  data: {
    name: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 下载胶片_订单形式
 * @param {object} params cmd
 * @param {number} params.downloadType 下载方式:1:直接下载，2：发送到邮箱
 * @param {string} params.email 邮箱
 * @param {number} params.orderId 订单id
 * @param {number} params.userId 用户id
 * @returns
 */
export const downloadByOrderApi = (params: DownloadByOrderParams, options?: any) =>
  useFetchApi('/api/downloadByOrder', {
    body: params,
    ...options,
  })
