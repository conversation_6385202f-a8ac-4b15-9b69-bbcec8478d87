<script lang="ts" setup>
import { UserFilled } from '@element-plus/icons-vue'
import { userStore } from '~/stores/userStore'
import HeaderNav from '~/components/app/HeaderNav.vue'

const useUserStore = userStore()
/* const { width } = useWindowSize()
const MAX_WIDTH = 430 */
const Gap = 4
const NavWidth = 110
const UnderLineWidth = 40

const { userCode } = storeToRefs(useUserStore)
const isShowMenu = ref(false)

const route = useRoute()
// console.log(route)
const lineLeft = computed(() => {
  let l = (NavWidth - UnderLineWidth) / 2
  const name = route.name?.split('-')[0]
  switch (name) {
    case 'tutorials':
      l = (NavWidth - UnderLineWidth) / 2 + Gap + NavWidth
      break
    case 'makeFilm':
      l = (NavWidth - UnderLineWidth) / 2 + (Gap + NavWidth) * 2
      break
    case 'infoReport':
      l = (NavWidth - UnderLineWidth) / 2 + (Gap + NavWidth) * 3
      break
    case 'miniShop':
      l = (NavWidth - UnderLineWidth) / 2 + (Gap + NavWidth) * 4
      break
    default:
      break
  }
  return l + 'px'
})

watch(route, () => {
  if (import.meta.client) {
    switch (route.query.msgType) {
      case 'success':
      case 'warning':
      case 'error':
        ElMessage({
          message: route.query?.msg as string,
          type: route.query.msgType,
          offset: 64,
        })
        break
    }
  }
}, { flush: 'pre', immediate: true, deep: true })

const handleLogout = () => {
  useUserStore.logout()
  useRouter().push({
    path: '/',
    query: {
      msgType: 'success',
      msg: '已登出',
    },
  })
}
</script>

<template>
  <div class="f-header bg-white sticky top-0 w-full box-border px-6 o-flex-c z-10">
    <div
      class="flex justify-between items-center w-full max-w-1536px relative"
    >
      <NuxtLink
        class="f-logo shrink-0 flex"
        to="/"
      >
        <NuxtImg
          alt="logo"
          class="f-logo-img"
          src="/resource/images/logo.png"
        />
        <!-- <div class="f-logo-img" /> -->
        <!-- <div class="text-lg sm:text-2xl font-bold mt-2 ml-2">
          条码帮
        </div> -->
      </NuxtLink>
      <div class="flex items-center gap-6 sm:gap-20">
        <div class="hidden lg:flex items-center">
          <nav class="flex relative text-lg">
            <HeaderNav />
            <span
              :style="{ left: lineLeft }"
              class="f-under-line absolute"
            />
          </nav>
        </div>
        <div class="block lg:hidden">
          <el-button
            text
            @click="isShowMenu = !isShowMenu"
          >
            <div class="i-ri:menu-line text-2xl" />
          </el-button>
        </div>
        <div
          v-show="isShowMenu"
          class="f-m-list absolute bg-white flex flex-col o-shadow"
        >
          <HeaderNav />
        </div>
        <div class="hidden sm:block">
          <el-button
            v-if="useUserStore.token === ''"
            href="/login"
            tag="a"
            target="_self"
            type="primary"
          >
            登录
          </el-button>
          <el-dropdown
            v-else
            trigger="click"
          >
            <div
              class="f-name flex items-center gap-4 cursor-pointer px-4"
            >
              <el-avatar
                :icon="UserFilled"
                size="small"
              />
              <div>
                {{ userCode }}
              </div>
            </div>

            <template #dropdown>
              <el-dropdown-menu>
                <!--              <el-dropdown-item @click="handleLogout">
                  个人中心
                </el-dropdown-item> -->
                <el-dropdown-item @click="handleLogout">
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
$h: 64px;
$lw: 136px;
$lh: 38px;
$c: 0.7;
$top: 53px;

.f-header {
  height: $h;
}

.f-logo {
  //width: $lw;
  //height: $lh;
}

.f-logo-img {
  // width: $lw;
  height: $lh;
  background-image: url('/resource/images/logo.png');
  background-size: cover;
}

@media screen and (width <= 640px) {
  .f-logo-img {
    // width: calc($lw * $c);
    height: calc($lh * $c);
  }
}

::v-deep(nav) {
  $gap: 4px;
  $nav-width: 110px;
  $under-line-width: 40px;

  gap: $gap;

  a {
    width: $nav-width;
    height: $h;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--el-text-color-regular);
    text-decoration: none;

    &::after {
      content: '';
      position: absolute;
      top: $top;
      left: calc(($nav-width - $under-line-width) / 2);
      width: $under-line-width;
      height: 3px;
      border-radius: 3px;
      background: var(--el-color-primary);
      transition: 0.5s;
      opacity: 0;
    }

    &:hover {
      background: rgba(225, 233, 255, 0.5);

      &::after {
        opacity: 1;
      }
    }
  }

  .f-under-line {
    @apply bg-blue;

    top: $top;
    left: calc(($nav-width - $under-line-width) / 2);
    width: $under-line-width;
    height: 3px;
    border-radius: 3px;
    transition: 0.5s;
    pointer-events: none;
  }
}

.f-name:hover {
  background: rgba(225, 233, 255, 0.5);
  height: $h;
}

::v-deep(.f-m-list) {
  @apply px-4 py-2;

  top: 58px;
  right: -21px;
  width: 160px;

  a {
    @apply text-center py-4 text-lg;

    color: var(--el-text-color-regular);
    text-decoration: none;
    border-bottom: 1px solid var(--el-border-color);

    &:active {
      color: var(--el-text-color-regular);
    }

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
