// https://nuxt.com/docs/api/configuration/nuxt-config

export default defineNuxtConfig({
  modules: [
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@nuxt/image',
    '@unocss/nuxt',
    '@nuxt/eslint',
    '@element-plus/nuxt',
    '@vueuse/nuxt',
  ],
  devtools: { enabled: true },
  app: {
    head: {
      script: [
        {
          src: 'https://hm.baidu.com/hm.js?211e5f80f60931d16a534dd1f9f856b4',
        },
      ],
    },
  },
  css: ['~/assets/css/index.scss'],
  // 运行时的一些全局变量
  runtimeConfig: {
    public: {
      myProxyUrl: 'api.gs1helper.com/', // 也用于ws
      apiBaseUrl: 'https://api.gs1helper.com',
    },
  },
  build: {
    transpile: ['pinia-plugin-persistedstate'],
  },
  devServer: {
    /*    https: {
      key: './config/ca.key',
      cert: './config/ca.crt',
    }, */
    host: '0.0.0.0',
    // host: '*************',
    port: 8083, // 如果没有设置 NUXT_PORT，则回退到默认端口 3000
  },
  compatibilityDate: '2024-04-03',
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          // 关于 Dart Sass 中遗留 JavaScript API 的 弃用警告 表示，该 API 将在 Dart Sass 2.0.0 中被移除。
          // 现代 API 特性
          api: 'modern-compiler',
          additionalData: `
          @use "~/assets/css/variable.scss" as *;
          @use "~/assets/css/elementPlusVariable.scss" as element;
        `,
        },
      },
    },
    esbuild: {
      drop: ['console'],
    },
    /*    server: {
      proxy: {
        '/wss': {
          target: 'wss://api.gs1helper.com',
          changeOrigin: true,
          ws: true,
          // rewrite: path => path.replace(/^\/wss/, ''),
          secure: true, // 当为 false 时，允许从 HTTPS 代理到 HTTP。通常情况下，你应该保持为 true。
          // 如果目标服务器有特定的头部需要传递或更改，可以在这里配置
          // headers: { ... }
        },
      },
    }, */
  },
  elementPlus: {
    icon: 'ElIcon',
    importStyle: 'scss',
    // themes: ['light'],
  },
  eslint: {
    config: {
      stylistic: true, // 格式化
    },
  },
  unocss: {
    nuxtLayers: true,
  },
  // plugins:[]
  // 生产环境
  // $production: {},
  // 开发环境
  // $development: {},
})
