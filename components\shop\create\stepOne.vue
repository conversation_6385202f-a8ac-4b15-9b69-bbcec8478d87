<script setup lang='ts'>
const useStepStore = miniShopCreateStepStore()
const {
  createType,
} = storeToRefs(useStepStore)
</script>

<template>
  <div class="f-h mt-4">
    <el-radio-group
      v-model="createType"
      class=" flex flex-col"
      style="align-items: flex-start;"
    >
      <el-radio value="report">
        从通报产品处导入
      </el-radio>
      <el-radio
        value="upload"
        disabled
      >
        批量上传
      </el-radio>
      <el-radio value="create">
        逐个填写
      </el-radio>
    </el-radio-group>
    <div
      class="mt-8"
    >
      <el-button
        type="primary"
        :disabled="!createType"
        @click="useStepStore.handleStep1"
      >
        下一步
      </el-button>
    </div>
  </div>
</template>

<style scoped lang='scss'>
.f-h{
  height: max(calc(100vh - 588px),300px);
}
</style>
