<script lang="ts" setup>
// 续期中转页
import <PERSON><PERSON> from 'bowser-ultralight'
import { createQrCode<PERSON>pi } from '~/composables/apiJump'

// 正式版为 release，体验版为 trial，开发版为 develop
const ENV_VERSION = 'release'
const { code } = useRoute().params
const isMobile = ref(true)

const { data } = await createQrCodeApi({
  dataCode: code,
})
const myQuery = encodeURIComponent('smsop=' + code)
const url = 'weixin://dl/business/?appid=wxdee9d93adbb330b8&path=pages/index/index&query=' + myQuery + '&env_version=' + ENV_VERSION

onMounted(() => {
  if (import.meta.client) {
    const browser = Bowser.getParser(window.navigator.userAgent)
    isMobile.value = browser.isMobile()
  }
})
</script>

<template>
  <div class="w-full box-border o-bg-blue-light py-6 sm:py-20 px-6 sm:px-20">
    <div class="f-box mx-auto bg-white rd-2 px-8 sm:px-16 pt-10 pb-10 relative overflow-hidden o-shadow">
      <div
        class="f-code-loader"
        style="position: absolute; top: 0; right: 53px"
      />
      <div
        class="font-black absolute z-1"
        style="color:#F0F3F8FF;top:-40px;left:-10px;font-size:120px;"
      >
        续
      </div>
      <div class="text-2xl font-black flex items-baseline flex-wrap mt-8 relative z-2">
        <div class="text-4xl mr-0.5">
          续
        </div>
        <div>展条码用户</div>
        <div>须知：</div>
      </div>
      <div>
        <p>
          条形码有效期为两年，到期前<strong
            class="px-2 mx-1"
            style="background-color: #e1e9f6;"
          >三个月</strong>内须进行续展。<span class="color-gray">（<span class="color-red mr-1">*</span>部分地区到期前一个月会禁止续期，请提前办理，避免错过时机。）</span>
        </p>
        <p>
          如超期限，条形码将自动注销，所有已上市商品条码均<span
            class="f-tip"
          >不可用</span>，并处于<span class="f-tip">违规</span>状态；且重新办理也不能恢复之前的编码。敬请重视。
        </p>
      </div>
      <div class="font-bold mt-6">
        办理所需资料：
      </div>
      <ol class="ml--4 mt-1">
        <li>联系人姓名 + 联系方式</li>
        <li>法人姓名 + 联系方式。</li>
        <li>邮箱 + 办公地址。</li>
        <li>营业执照副本复印件，空白处加盖公章。</li>
      </ol>
      <div class="py-6">
        <div class="font-bold">
          相关知识：
        </div>
        <ul class="ml--4">
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/55"
              target="_blank"
            >条码注销后商品条码还能继续使用吗？</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/59"
              target="_blank"
            >销售者禁止经销哪些违反《商品条码管理办法》规定的商品？有何处罚？</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/56"
              target="_blank"
            >条码不想用了，可以注销吗？</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/64"
              target="_blank"
            >企业可以使用已被注销的厂商识别代码吗？</a>
          </li>
          <li>
            <a
              href=""
              style="color:#838383"
              target="_blank"
            >更多请前往小程序查看...</a>
          </li>
        </ul>
      </div>
      <a
        v-if="isMobile"
        class="mx-auto text-lg block bg-blue color-white font-bold py-6 px-10 rd-1 w-fit text-center"
        :href="url"
      >

        <div>点击前往小程序</div>
        <strong class="text-xl mt-2 font-bold">办理续展业务</strong>
      </a>
      <div
        v-else
        class="mx-auto w-fit text-center o-bg-blue-light rd-1 py-4 px-8"
      >
        <div>微信扫码，即可前往小程序</div>
        <strong class="text-xl mt-2">办理续展业务</strong>
      </div>
      <NuxtImg
        alt="小程序二维码"
        class="mx-auto block mt-4"
        :src="'data:image/png;base64,'+data?.data"
        style="width: 200px;height: 200px;"
      />
      <div class="color-gray text-sm text-center">
        微信扫一扫
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.f-box {
  max-width: 600px;
}

ul {
  &>li {
    @apply mb-1;
  }
}

li {
  &::marker {
    @apply color-blue font-bold leading-8;
  }

  a {
    color: #4f85ff;
  }
}

p {
  text-indent: 2em;
}

.f-tip {
  @apply px-2 mx-1;
  background: #ffecec;
  color: #e40000;
}

.f-code-loader {
  width: 45px;
  height: 40px;
  background: linear-gradient(
      #0000 calc(1 * 100% / 6),
      #000 0 calc(3 * 100% / 6),
      #0000 0
  ),
  linear-gradient(
      #0000 calc(2 * 100% / 6),
      #000 0 calc(4 * 100% / 6),
      #0000 0
  ),
  linear-gradient(
      #0000 calc(3 * 100% / 6),
      #000 0 calc(5 * 100% / 6),
      #0000 0
  );
  background-size: 10px 400%;
  background-repeat: no-repeat;
  animation: f-matrix-1555 1s infinite linear;
}

@keyframes f-matrix-1555 {
  0% {
    background-position: 0% 100%, 50% 100%, 100% 100%;
  }

  100% {
    background-position: 0% 0%, 50% 0%, 100% 0%;
  }
}
</style>
