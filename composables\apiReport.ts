// 参数接口
import { useFetchApi } from '~/composables/api'

export interface ReportGoodsPageParams {
  barCode?: string
  brandName?: string
  certificationId?: number
  endDate?: string
  goodsName?: string
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirection
  pageIndex?: number
  pageSize?: number
  startDate?: string
  userId: number
}

export interface ReportGoodsPageResData {
  barCode: string
  barType: string
  brandName: string
  certificationId: number
  commonName: string
  companyPrice: number
  currency: string
  goodsDescription: string
  goodsId: number
  goodsName: string
  goodsType: string
  gpcType: string
  gpcTypeName: string
  isPrivary: boolean
  lastUpdatedDate: Record<string, unknown>
  marketDate: string
  netContent: string
  productFeatures: string
  spec: string
}

// 响应接口
export interface ReportGoodsPageRes {
  data: ReportGoodsPageResData[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 通报商品分页
 * @param {object} params qry
 * @param {string} params.barCode 条码信息
 * @param {string} params.brandName 品牌名称
 * @param {number} params.certificationId 认证id/企业id，因为前期不需要认证，所以不需要填写
 * @param {string} params.endDate 修改结束时间
 * @param {string} params.goodsName 产品名称
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {string} params.startDate 修改开始时间
 * @param {number} params.userId 用户id
 * @returns
 */
export const reportGoodsPageApi = (params: ReportGoodsPageParams, options?: any) =>
  useFetchApi<ReportGoodsPageRes>('/api/reportGoodsPage', {
    body: params,
    ...options,
  })

// 参数接口
export interface ReportFileRecordPageParams {
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirection
  pageIndex?: number
  pageSize?: number
  userId: number
}

export interface ReportFileRecordPageResData {
  fileName: string
  fileRecordId: number
  fileState: number // 文件状态：-1：未满足条件，0：处理中，1：通报完成
  fileStateName: string
  fileUrl: string
  remark: string
  size: number
  suffix: string
  uploadDate: Record<string, unknown>
  userId: number
}

// 响应接口
export interface ReportFileRecordPageRes {
  data: ReportFileRecordPageResData[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 通报文件记录分页
 * @param {object} params qry
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {number} params.userId 用户id
 * @returns
 */
export const reportFileRecordPageApi = (params: ReportFileRecordPageParams, options?: any) =>
  useFetchApi<ReportFileRecordPageRes>('/api/reportFileRecordPage', {
    body: params,
    transform: (res: ReportFileRecordPageRes) => res.data,
    ...options,
  })

export const reportFileRecordCreatePath = '/api/reportFileRecordCreate' // 上传通报文件记录

// 参数接口
export interface ReportFileRecordDelParams {
  fileRecordId: number
}

// 响应接口
export interface ReportFileRecordDelRes {
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 删除通报文件记录
 * @param {object} params cmd
 * @param {number} params.fileRecordId 记录id
 * @returns
 */
export const reportFileRecordDelApi = (params: ReportFileRecordDelParams, options?: any) =>
  fetchApi<ReportFileRecordDelRes>('/api/reportFileRecordDel', {
    method: 'POST',
    body: params,
    ...options,
  })

export const reportFileRecordUpdatePath = '/api/reportFileRecordUpdate' // 编辑通报文件记录
export const downloadReportPath = '/api/downloadReport' // 下载通报文件
