<script setup lang='ts'>
import { ArrowRight } from '@element-plus/icons-vue'
import { MENU } from '~/config/menu'

const route = useRoute()

const items = computed(() => {
  for (let i = 0; i < MENU.length; i++) {
    for (let j = 0; j < MENU[i].items.length; j++) {
      if (MENU[i].items[j].route === route.path) {
        return [{ label: MENU[i].label }, { label: MENU[i].items[j].label }]
      }
    }
  }
  return []
},
)
</script>

<template>
  <div class="max-w-1536px mx-auto flex gap-4">
    <AppLeftMenu />
    <div class="f-right-box grow-1 flex flex-col">
      <el-breadcrumb
        class="pl-4 py-5 mt-2"
        :separator-icon="ArrowRight"
      >
        <el-breadcrumb-item :to="{ path: '/' }">
          <div class="i-ri:home-2-line" />
        </el-breadcrumb-item>
        <el-breadcrumb-item
          v-for="i in items"
          :key="i.label"
        >
          {{ i.label }}
        </el-breadcrumb-item>
      </el-breadcrumb>
      <div class="rd-2 bg-white p-8 grow-1">
        <slot />
      </div>
    </div>
  </div>
</template>

<style scoped lang='scss'>
.f-right-box {
  max-width: calc(100% - var(--cf-left-menu-width) - 2rem);
}
</style>
