import { getRandomFileName } from '~/utils'
import { OssFilePath } from '~/composables/enum'

export const useShopCreateTiny = (ossClient: Ref<any>) => {
  const contentHTML = ref('')

  const uploadImageToOSS = (blobInfo, progress) => new Promise((resolve, reject) => {
    console.log('blobInfo', blobInfo)
    const file = blobInfo.blob()
    console.log('blobInfo.blob', file)
    console.log('file instanceof Blob', file instanceof Blob)
    console.log('file instanceof File', file instanceof File)

    const maxSize = 2 * 1024 * 1024 // 最大文件大小为2MB

    if (file.size > maxSize) {
      reject(`文件大小超过${maxSize}MB，请压缩后上传`)
    }
    else {
      const filePathAndName = getRandomFileName(OssFilePath.descriptionImg, file.name)
      ossClient.value.put(filePathAndName, file).then((res) => {
        resolve(ossClient.value.signatureUrl(res.name))
      }).catch((err) => {
        reject(err)
      })
    }
  })

  type BlobInfo = {
    blob: () => File
    file: File
  }

  const uploadManyImageToOSS = (blobInfo: BlobInfo, succFun, failFun) => {
    let file = blobInfo.blob()
    // console.log('blobInfo', blobInfo)
    // console.log('file', file)
    // const b = Object.getPrototypeOf(file)
    // const b = Object.getPrototypeOf(Object.getPrototypeOf(file)).blob()
    // console.log('file.__proto__', b)
    const maxSize = 2 * 1024 * 1024 // 最大文件大小为2MB
    // console.log('file', blobInfo)
    console.log('file instanceof Blob', file instanceof Blob)
    console.log('file instanceof File', file instanceof File)

    if (!(file instanceof File)) {
      file = new File([file], file.name)
    }

    console.log('file instanceof Blob', file instanceof Blob)
    console.log('file instanceof File', file instanceof File)
    if (file.size > maxSize) {
      failFun(`文件大小超过${maxSize / (1024 * 1024)}MB，请压缩后上传`)
      return // 确保在这里返回以防止继续执行
    }

    const filePathAndName = getRandomFileName(OssFilePath.descriptionImg, file.name)

    ossClient.value.put(filePathAndName, file)
      .then((res) => {
        succFun(ossClient.value.signatureUrl(res.name))
      })
      .catch((err) => {
        failFun(err)
      })
  }

  /*  plugins: 'preview importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount help charmap quickbars emoticons accordion axupimgs',
    toolbar: [
    'undo redo | blocks fontsize bold italic underline strikethrough emoticons table',
    'align numlist bullist lineheight outdent indent | link image axupimgs media | forecolor backcolor removeformat | code fullscreen preview',
  ], */

  // http://tinymce.ax-z.cn
  const editInit = {
    height: 600,
    menubar: false,
    plugins: 'preview importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists charmap quickbars emoticons accordion axupimgs',
    toolbar: [
      'undo redo blocks fontsize bold italic underline strikethrough emoticons table fullscreen',
      'align numlist bullist lineheight outdent indent link image axupimgs media forecolor backcolor removeformat code',
    ],
    automatic_uploads: true,
    file_picker_types: 'image',
    language: 'zh_CN',
    language_url: '/resource/other/Tinymce_zh_CN.js',
    branding: false,
    promotion: false, // 去掉右上角的Upgrade按钮
    resize: false, // 可选值为：true（仅允许改变高度）, false（完全不让你动）, 'both'（宽高都能改变，注意引号）
    images_upload_handler: uploadImageToOSS,
    many_images_upload_handler: uploadManyImageToOSS,
  }

  return { contentHTML, editInit }
}
