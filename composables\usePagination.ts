import { useWindowSize } from '@vueuse/core'

export const usePagination = (reduce: number, min: number) => {
  const { height } = useWindowSize()

  const pageSize = ref(20)
  const pageIndex = ref(1)
  const totalCount = ref(0)

  const tableHeight = computed(() => {
    if (import.meta.client) {
      return Math.max(height.value - reduce, min) || min
    }
    else {
      return min
    }
  })

  return {
    pageSize,
    pageIndex,
    totalCount,
    tableHeight,
  }
}
