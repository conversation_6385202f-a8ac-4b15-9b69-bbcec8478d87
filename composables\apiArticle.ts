import { useFetchApi } from '~/composables/api'
import type { FromPlatform } from '~/composables/enum'

interface articleListParams {
  articleTypeId: number
}

/**
 * 文章列表,只显示可用类型的文章
 * @param params
 * @param options
 */
export const useArticleListData = (params: articleListParams, options?: any) =>
  useFetchApi<any>('/api/articleList', {
    body: params,
    transform: data => data.data,
    ...options,
  })
/*
export const getNewsList = async (params?: articleListParams, option?: HttpOption<any>) =>
  await useHttp.post<any>(articleListPath, params, { ...option })
*/

// 参数接口
export interface ArticleLoadParams {
  articleId: number
}
interface ArticleLoadResData {
  articleConent: string
  articleFlag: number
  articleId: number
  articleTitle: string
  articleUrl: string
  imageUrl: string
  sortNum: number
}
// 响应接口
export interface ArticleLoadRes {
  data: ArticleLoadResData
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 文章加载
 * @param {object} params qry
 * @param {number} params.articleId 文章id
 * @returns
 */
export const useArticleLoadData = (params: ArticleLoadParams, options?: any) =>
  useFetchApi<ArticleLoadResData>('/api/articleLoad', {
    body: params,
    transform: (data: ArticleLoadRes) => data.data,
    ...options,
  })

// 获取小程序登录二维码Qr Code
// const wxparentsUserGetLoginQrCodePath = '/wxparents/user/getLoginQrCode'
/*
export const useGetLoginQrCodeData = async (params: {
  uuid: string
}, transform?: any) =>
  await getPostData('wxparentsUserGetLoginQrCode', wxparentsUserGetLoginQrCodePath, params, transform)
*/

// 参数接口
export interface GetLoginQrCodeParams {
  uuid?: string
  fromTo?: FromPlatform
}

// 响应接口
export interface GetLoginQrCodeRes {
  data: string
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 获取小程序登录二维码Qr Code
 * @param {object} params cmd
 * @param {string} params.uuid
 * @returns
 */
export const useGetLoginQrCodeData = (params: GetLoginQrCodeParams, options?: any) =>
  useFetchApi<string>('/wxparents/user/getLoginQrCode', {
    body: params,
    transform: (data: GetLoginQrCodeRes) => data.data,
    ...options,
  })
