/* 只需要重写你需要的即可 */
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
   $colors: (
    'primary': (
      'base': #165DFF,
    ),
    'success': (
      'base': #21C33B,
    ),
    'warning': (
        'base': #F79F23,
    ),
    'danger': (
      'base': #DC3126,
    ),
    'error': (
      'base': #DC3126,
    ),
  ),
  $text-color:(
    'primary': #000000E0,
    'regular': #000000A6,
  ),
  $box-shadow:(
      '': (
      0px 12px 32px 4px rgba(22, 93, 255, 0.04),
      0px 8px 20px rgba(22, 93, 255, 0.08),
    ),
    'light': (
      0px 0px 12px rgba(22, 93, 255, 0.12),
    ),
    'lighter': (
      0px 0px 6px rgba(22, 93, 255, 0.12),
    ),
    'dark': (
      0px 16px 48px 16px rgba(22, 93, 255, 0.08),
      0px 12px 32px rgba(22, 93, 255, 0.12),
      0px 8px 16px -8px rgba(22, 93, 255, 0.16),
    ),
  )
/*  $button:(
  'hover-bg-color': getCssVar('color-primary', 'dark-2'),
  )*/
/*  $border-radius: (
    'base': 6px,
  ),*/
);

// 如果只是按需导入，则可以忽略以下内容。
// 如果你想导入所有样式:
// @use "element-plus/theme-chalk/src/index.scss" as *;
