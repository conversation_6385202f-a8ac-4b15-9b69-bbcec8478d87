export default defineEventHandler(async (event) => {
  if (event.node.req.url?.includes('/baseApi')) {
    console.log('event.node.req:', event.node.req)
    const { method, url } = event.node.req
    const options: any = {}
    options.headers = {
      'content-type': 'application/json',
      // accept: 'application/json'
    }
    options.method = method
    if (method !== 'get' && method !== 'GET') {
      options.body = JSON.stringify(await readBody(event))
    }
    const path = url.replace(/^\/baseApi\//, '')
    const r = 'https://' + useRuntimeConfig().public.myProxyUrl + path
    console.log('url', r)
    return await $fetch(r, options)
  }
})
