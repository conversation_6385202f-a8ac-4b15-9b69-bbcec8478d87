{
  // "editor.defaultFormatter": "esbenp.prettier-vscode",
  // "editor.formatOnSave": true, // 保存时自动格式化
  // "editor.formatOnType": true, // 输入时自动格式化
  "editor.formatOnSave": false, // 禁用 VSCode 默认格式化
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "eslint.autoFixOnSave": true, // 启用保存时自动修复
  "eslint.validate": [
    {
      "language": "javascript",
      "autoFix": true
    },
    {
      "language": "vue",
      "autoFix": true
    },
    {
      "language": "html",
      "autoFix": true
    }
  ]
}
