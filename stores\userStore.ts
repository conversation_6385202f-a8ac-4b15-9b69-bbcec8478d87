export const userStore = defineStore(
  'userStore',
  () => {
    // const EXPIRATION = 3600000 * 2 - 10000
    const token = ref('')
    const userName = ref('')
    const userCode = ref('')
    const userId = ref('')

    const setToken = (newToken: string) => {
      token.value = newToken
    }
    const getToken = () => {
      return token.value
    }

    const logout = () => {
      token.value = ''
      userName.value = ''
      userCode.value = ''
      userId.value = ''
    }

    return {
      userCode,
      userId,
      token,
      userName,
      setToken,
      getToken,
      logout,
    }
  },
  {
    // TODO 更新了依赖需要验证
    persist: true, // 默认放cookie 中
  },
)
