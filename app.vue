<script setup lang="ts">
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import <PERSON><PERSON> from 'bowser-ultralight'
// <meta name="viewport" content="width=device-width, initial-scale=1">
useHead({
  title: '条码帮-商品条码服务中心平台',
  meta: [
    {
      name: 'keywords',
      content: '条码帮,商品条码,物品编码,一维码制作,二维码制作',
    },
    {
      name: 'description',
      content: '条码帮 商品条码服务中心专业平台',
    },
    // { name: 'viewport', content: 'width=device-width, initial-scale=1.0, viewport-fit=cover' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' },
  ],
  link: [
    {
      rel: 'icon',
      type: 'image/x-icon',
      sizes: '16x16',
      href: '/favicon-16x16.ico',
    },
    {
      rel: 'icon',
      type: 'image/x-icon',
      sizes: '32x32',
      href: '/favicon-32x32.ico',
    },
    {
      rel: 'icon',
      type: 'image/x-icon',
      sizes: '64x64',
      href: '/favicon-64x64.ico',
    },
    {
      rel: 'icon',
      type: 'image/x-icon',
      sizes: '128x128',
      href: '/favicon-128x128.ico',
    },
  ],
})
onMounted(() => {
  if (import.meta.client) {
    const browser = Bowser.getParser(window.navigator.userAgent)
    const browserVersion = browser.getBrowserVersion()
    const isMobile = browser.isMobile()
    if (!isMobile) {
      const isWindows = () => {
        // 判断是否包含Windows相关的关键字
        return /Windows/.test(window.navigator.userAgent)
      }

      if (isWindows()) {
        const v = Number(browserVersion?.split('.')[0])
        if (v < 100) {
          showBrowserTip.value = true
          alert(
            '当前浏览器内核为'
            + v
            + '，版本过低，请尽快升级至最新版本以获取最佳体验。',
          )
        }
      }
    }
  }
})
const showBrowserTip = ref(false)
</script>

<template>
  <el-config-provider :locale="zhCn">
    <el-alert
      v-if="showBrowserTip"
      title="当前浏览器内核版本过低，请尽快升级至最新版本以获取最佳体验。"
      type="error"
      effect="dark"
    />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </el-config-provider>
</template>

<style scoped lang="scss"></style>
