<script setup lang='ts'>
import { reportGoodsPageApi, type ReportGoodsPageResData } from '~/composables/apiReport'
import { usePagination } from '~/composables/usePagination'

const {
  pageSize,
  pageIndex,
  totalCount,
  tableHeight,
} = usePagination(320, 300)
const list = ref<ReportGoodsPageResData[]>([])
const formInline = reactive({
  barCode: '',
  goodsName: '',
})
const formBarCode = ref('')
const formGoodsName = ref('')

const { data, status, error, refresh, clear } = await reportGoodsPageApi({
  barCode: formBarCode,
  goodsName: formGoodsName,
  needTotalCount: true,
  orderBy: '',
  orderDirection: OrderDirection.def,
  pageIndex: pageIndex,
  pageSize: pageSize,
  userId: Number(userStore().userId),
})

watch(data, () => {
  if (data.value?.data) {
    list.value = data.value.data
    totalCount.value = data.value.totalCount
  }
}, { immediate: true })

const loading = computed(() => status.value === 'pending')

const handleSearch = () => {
  formBarCode.value = formInline.barCode
  formGoodsName.value = formInline.goodsName
}
const handleReset = () => {
  pageIndex.value = 1
  formInline.barCode = ''
  formInline.goodsName = ''
  formBarCode.value = ''
  formGoodsName.value = ''
}
</script>

<template>
  <div class="w-full o-bg-blue-light">
    <AppMenuAndContent>
      <el-form
        :inline="true"
        :model="formInline"
      >
        <el-form-item label="商品条码">
          <el-input
            v-model="formInline.barCode"
            style="width: 180px"
            clearable
            @keydown.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="产品名称">
          <el-input
            v-model="formInline.goodsName"
            style="width: 180px"
            clearable
            @keydown.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleSearch"
          >
            <div class="i-ri:search-line mr-2" />
            查询
          </el-button>
          <el-button
            @click="handleReset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <ClientOnly>
        <el-table
          v-loading="loading"
          :data="list"
          :height="tableHeight"
          style="width: 100%"
          row-key="goodsId"
        >
          <el-table-column
            prop="barCode"
            label="商品条码"
            width="160"
          />
          <el-table-column
            prop="goodsName"
            label="产品名称"
            width="180"
          >
            <template #default="scope">
              <div class="o-inline-2">
                {{ scope.row.goodsName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="commonName"
            label="产品通用名"
            width="180"
          >
            <template #default="scope">
              <div class="o-inline-2">
                {{ scope.row.commonName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="brandName"
            label="品牌名称"
          />
          <el-table-column
            prop="productFeatures"
            label="产品特征"
          />
          <el-table-column
            prop="gpcTypeName"
            label="产品GPC分类"
            width="180"
          />
          <el-table-column
            prop="netContent"
            label="净含量"
            width="100"
          />
          <el-table-column
            prop="spec"
            label="规格"
            width="100"
          />
          <el-table-column
            prop="lastUpdatedDate"
            label="更新时间"
            width="170"
          />
          <el-table-column
            prop="isPrivary"
            label="保密"
          >
            <template #default="scope">
              <el-tag
                v-if="scope.row.isPrivary"
                type="danger"
              >
                是
              </el-tag>
              <el-tag
                v-else
                type="primary"
              >
                否
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="marketDate"
            label="（预计）上市时间"
            width="140"
          />
          <el-table-column
            prop="goodsType"
            label="产品状态"
          />
          <el-table-column
            prop="goodsDescription"
            label="产品描述"
            width="180"
          >
            <template #default="scope">
              <div class="o-inline-4">
                {{ scope.row.goodsName }}
              </div>
            </template>
          </el-table-column>
          <!--        <el-table-column
          prop="goodsName"
          label="产品执行标准"
          width="180"
        >
          <template #default="scope">
            <div class="o-inline-4">
              {{ scope.row.goodsName }}
            </div>
          </template>
        </el-table-column> -->
          <el-table-column
            prop="companyPrice"
            label="企业定价"
            width="100"
          >
            <template #default="scope">
              <div class="">
                <div>{{ scope.row.companyPrice }}</div>
                <div>{{ scope.row.currency }}</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </ClientOnly>
      <el-pagination
        v-model:current-page="pageIndex"
        v-model:page-size="pageSize"
        class="float-right mt-8"
        :page-sizes="[20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalCount"
      />
    </AppMenuAndContent>
  </div>
</template>

<style scoped lang='scss'></style>
