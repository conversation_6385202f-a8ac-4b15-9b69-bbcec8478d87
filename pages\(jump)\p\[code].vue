<script setup lang="ts">
// 图片通报中转页
import <PERSON><PERSON> from 'bowser-ultralight'
import { createQrCode<PERSON><PERSON> } from '~/composables/apiJump'

// 正式版为 release，体验版为 trial，开发版为 develop
const ENV_VERSION = 'release'
const { code } = useRoute().params
const isMobile = ref(true)

const { data } = await createQrCodeApi({
  dataCode: code,
})
const myQuery = encodeURIComponent('smsop=' + code)
const url = 'weixin://dl/business/?appid=wxdee9d93adbb330b8&path=pages/index/index&query=' + myQuery + '&env_version=' + ENV_VERSION

onMounted(() => {
  if (import.meta.client) {
    const browser = Bowser.getParser(window.navigator.userAgent)
    isMobile.value = browser.isMobile()
  }
})
</script>

<template>
  <div class="w-full box-border o-bg-blue-light py-6 sm:py-20 px-6 sm:px-20">
    <div class="f-box mx-auto bg-white rd-2 px-8 sm:px-16 pt-10 pb-10 relative overflow-hidden o-shadow">
      <div
        class="f-code-loader"
        style="position: absolute; top: 0; right: 53px"
      />
      <div
        class="font-black absolute z-1"
        style="color:#F0F3F8FF;top:-40px;left:-10px;font-size:120px;"
      >
        查
      </div>
      <div class="text-2xl font-black flex items-baseline flex-wrap mt-8 relative z-2">
        <div class="text-4xl mr-0.5">
          产
        </div>
        <div>品信息自查通知：</div>
      </div>
      <div>
        <p class="o-p">
          您的商品条码信息
          <strong
            class="o-bg-gray-light"
            style="padding:3px 0px 3px 10px;"
          >尚未完善，缺少关键内容（产品图片）</strong>。为保障商品商超扫码体验，请及时登录系统补充相关信息。感谢您的配合！
        </p>
      </div>
      <div class="py-6">
        <div class="font-bold">
          相关知识：
        </div>
        <ul class="ml--4">
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/20"
              target="_blank"
            >哪些产品信息可以同步至微信？</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/19"
              target="_blank"
            >产品图片是否必须上传？</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/17"
              target="_blank"
            >产品信息修改后，微信扫码数据未更新</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/16"
              target="_blank"
            >产品通报时名称是否要与包装上完全一致？</a>
          </li>
          <li>
            <a
              style="color:#838383"
              href="https://www.gs1helper.com/tutorials/45"
              target="_blank"
            >更多请前往小程序查看...</a>
          </li>
        </ul>
      </div>
      <a
        v-if="isMobile"
        class="mx-auto text-lg block bg-blue color-white font-bold py-6 px-10 rd-1 w-fit"
        :href="url"
      >
        点击前往小程序办理
      </a>
      <NuxtImg
        class="mx-auto block mt-4"
        :src="'data:image/png;base64,'+data?.data"
        alt="小程序二维码"
        style="width: 200px;height: 200px;"
      />
      <div
        class="color-gray mt-2 text-sm text-center"
      >
        微信扫一扫
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.f-box {
  max-width: 600px;
}

li {
  @apply mb-1;

  &::marker {
    @apply color-blue font-bold leading-8;
  }

  a {
    color: #4f85ff;
  }
}

.f-code-loader {
  width: 45px;
  height: 40px;
  background: linear-gradient(
      #0000 calc(1 * 100% / 6),
      #000 0 calc(3 * 100% / 6),
      #0000 0
  ),
  linear-gradient(
      #0000 calc(2 * 100% / 6),
      #000 0 calc(4 * 100% / 6),
      #0000 0
  ),
  linear-gradient(
      #0000 calc(3 * 100% / 6),
      #000 0 calc(5 * 100% / 6),
      #0000 0
  );
  background-size: 10px 400%;
  background-repeat: no-repeat;
  animation: f-matrix-1555 1s infinite linear;
}

@keyframes f-matrix-1555 {
  0% {
    background-position: 0% 100%, 50% 100%, 100% 100%;
  }

  100% {
    background-position: 0% 0%, 50% 0%, 100% 0%;
  }
}
</style>
