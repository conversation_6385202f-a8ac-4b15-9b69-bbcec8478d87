module.exports = {
  apps: [
    {
      name: 'barcode_web_platform', // 应用名称
      port: '8888', // 端口号
      exec_mode: 'fork', // cluster 集群模式，如不指定，默认为fork
      instances: '1', // 集群实例数，fork 模式
      script: './barcode_web_platform/.output/server/index.mjs', // 执行的脚本路径
    },
    {
      name: 'barcode_mini_stie', // 应用名称
      port: '8889', // 端口号
      exec_mode: 'fork', // cluster 集群模式，如不指定，默认为fork
      instances: '1', // 集群实例数，fork 模式
      script: './barcode_mini_stie/.output/server/index.mjs', // 执行的脚本路径
    },
  ],
}
