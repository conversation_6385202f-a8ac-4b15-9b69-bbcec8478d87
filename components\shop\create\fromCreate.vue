<script lang="ts" setup>
const useStepStore = miniShopCreateStepStore()
const { activeStep } = storeToRefs(useStepStore)

const editorRef = ref()
const dialogVisible = ref(true)
const handleSuccess = () => {
  activeStep.value = 3
}
const handleNext = () => {
  editorRef.value.submitForm()
}
</script>

<template>
  <ShopEditor
    ref="editorRef"
    v-model:dialog-visible="dialogVisible"
    :is-create="true"
    @success="handleSuccess"
  />
  <el-button
    class="mt-8 mr-20"
    @click="useStepStore.stepBack"
  >
    上一步
  </el-button>
  <el-button
    class="mt-8"
    type="primary"
    @click="handleNext"
  >
    下一步
  </el-button>
</template>

<style lang="scss" scoped>
</style>
