// 将https://b2b.gds.org.cn/coding/GetGPC接口数据转成符合element-plus的选择项结构
const fs = require('fs')
const path = require('path')
// 读取 JSON 文件
const jsonData = JSON.parse(fs.readFileSync('./build/gpcData.JSON', 'utf-8'))

// 将扁平的 JSON 转换为层次结构
function buildHierarchy(data) {
  const idMap = {}

  // 初始化节点映射
  data.forEach((item) => {
    idMap[item.ID] = {
      // hierarchy: item.hierarchy,
      value: item.Code,
      label: item.Description,
      // label_en: item.Description_en,
      children: [],
    }
  })

  // 构建层次结构
  const result = []
  data.forEach((item) => {
    if (item.PID === 0) {
      result.push(idMap[item.ID])
    }
    else {
      if (idMap[item.PID]) {
        idMap[item.PID].children.push(idMap[item.ID])
      }
    }
  })

  return result
}

// 生成层次化结构
const hierarchyData = buildHierarchy(jsonData)

// 生成 TypeScript 文件内容
const tsContent = `export const gpcData = ${JSON.stringify(hierarchyData, null, 2)};`

// 保存为 TypeScript 文件
fs.writeFileSync(path.join(__dirname, '../composables/gpcData.ts'), tsContent, 'utf-8')
console.log('转换完成，数据已保存到 gpcData.ts 文件中')
