import type { FetchResponse } from 'ofetch'
import { userStore } from '~/stores/userStore'

/**
 * 截取字符串
 * @param str
 */
function truncateString(str: string) {
  if (str?.length > 40) {
    return str.slice(0, 40) + '...' // 截断并添加省略号
  }
  return str // 返回原字符串
}

const doOnResponse = (response: FetchResponse<any>, failed?: any) => {
  const refreshToken = response.headers.get('refreshtoken')
  if (refreshToken !== null && refreshToken !== '') {
    userStore().setToken(refreshToken)
  }
  // 当response.status为401时，onResponse和onResponseError都会执行
  if (response.status >= 200 && response.status < 300) {
    const d = response._data
    if (!d.success) {
      if (failed) failed.value = true
      if (d?.errMessage === 'Token过期') {
        userStore().logout()
        navigateTo({
          path: '/login',
          query: {
            msgType: 'error',
            msg: '登录过期，请重新登录',
          },
        })
      }
      else {
        // response._data里面是后天返回的数据，这里code跟上面的code不是同一个
        // _data.code 是后台跟前端约定的状态码，或者是约定_data.success 判断

        // 这里如果要给用户提示错误，要分服务端还是客户端
        // console.log(response._data.errMessage)
        if (import.meta.client) {
          ElMessage.error('错误：' + truncateString(response._data.errMessage))
        }
        else {
          if (import.meta.client) {
            // 跳转到错误提示页
            useNuxtApp().runWithContext(() => {
              navigateTo({
                path: '/myError',
                query: {
                  msgType: 'error',
                  msg: truncateString(response._data.errMessage),
                },
              })
            })
          }
        }
      }
    }
    else {
      if (failed) failed.value = false
    }
  }
}

const doOnResponseError = (response: FetchResponse<any>) => {
  const nuxtApp = useNuxtApp()
  if (import.meta.client) {
    ElMessage.error('客户端错误')
  }
  else {
    console.log('onResponseError')
    // 跳转到错误提示页
    nuxtApp.runWithContext(() => {
      navigateTo({
        path: '/myError',
        query: {
          msgType: 'error',
          msg: truncateString(response._data.errMessage),
        },
      })
    })
  }
}

/**
 * useFetch封装，用于服务端也能获取数据，后台data.success=false时，status会返回失败
 * 只能用于setup顶层，不能放其他函数里，详见nuxt useFetch用法
 * @param url
 * @param options
 */
export const useFetchApi = async <T>(url: string, options?: any) => {
  // console.log('useRuntimeConfig().public.apiBaseUrl', useRuntimeConfig().public.apiBaseUrl)
  const failed = ref(false)
  const {
    data: fetchData,
    status: fetchStatus,
    error: fetchError,
    refresh,
    clear,
  }
    = await useFetch<T>(useRuntimeConfig().public.apiBaseUrl + url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': userStore().getToken(),
      },
      /*    onRequest({ options }) {
      options.headers.set('Authorization', userStore().getToken())
    }, */
      onResponse({ response }) {
        doOnResponse(response, failed)
      },
      onResponseError({ response }) {
      // 这里如果要给用户提示错误，要分服务端还是客户端
        doOnResponseError(response)
      },
      ...options,
    })

  const status = computed<'idle' | 'pending' | 'success' | 'error'>(() => {
    if (failed.value) {
      return 'error'
    }
    return fetchStatus.value
  })
  const data = computed(() => {
    return fetchData.value
  })
  const error = computed(() => {
    return fetchError.value
  })

  return { data, status, error, refresh, clear }
}

/**
 * $fetch封装，用于方便用then获取数据
 */
export const fetchApi = $fetch.create({
  async onRequest({ options }) {
    // 在请求中添加全局的 baseURL 和 headers
    options.baseURL = useRuntimeConfig().public.apiBaseUrl
    options.headers.set('Content-Type', 'application/json')
    options.headers.set('Authorization', userStore().getToken())
  },

  async onResponse({ response }) {
    doOnResponse(response)
  },

  async onResponseError({ response }) {
    doOnResponseError(response)
  },
})

/**
 * 原生fetch封装，用于获取原始数据，不进行任何处理。（针对$fetch获取二维码图片失败用）
 * @param url
 */
export const originalFetchApi = (url: string) => new Promise((resolve, reject) => {
  fetch(
    useRuntimeConfig().public.apiBaseUrl + url,
    {
      method: 'GET',
      headers: {
        Authorization: userStore().getToken(),
      },
    }).then((response) => {
    // 检查响应是否成功
    if (!response.ok) {
      throw new Error(`网络响应错误: ${response.status}`)
    }
    resolve(response.body)
    // 不能少
    return response.body
  }).catch((error) => {
    reject(error)
  })
})
