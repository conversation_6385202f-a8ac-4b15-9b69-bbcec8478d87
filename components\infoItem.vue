<script setup lang="ts">
defineProps({
  title: {
    type: String,
    required: true,
  },
  eng: {
    type: String,
    required: true,
  },
  needContent: {
    type: Boolean,
    required: false,
    default: true,
  },
  content: {
    type: [String, Array],
    required: false,
    default: null,
  },
  miniContent: {
    type: String,
    required: false,
    default: null,
  },
})
</script>

<template>
  <div
    v-if="!needContent || (content !== null && content !== undefined)"
    class="f-info-item"
  >
    <div class="color-gray font-bold text-sm o-font">
      {{ title }}
    </div>
    <div class="color-gray text-xs o-font line-height-2">
      {{ eng }}
    </div>
    <div
      v-if="Array.isArray(content)"
      class="font-bold mt-2"
    >
      <div
        v-for="(item, index) in content"
        :key="index"
      >
        {{ item }}
      </div>
    </div>
    <div
      v-else
      class="font-bold mt-2"
    >
      {{ content
      }}<span
        v-if="miniContent"
        class="text-sm"
      >（{{ miniContent }}）</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.f-info-item {
  min-width: 37.7vw;
}
</style>
