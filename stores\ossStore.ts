import { getStsTokenForOssApi } from '~/composables/apiMiniShop'

export const ossStore = defineStore(
  'ossStore',
  () => {
    const accessKeyId = ref('')
    const accessKeySecret = ref('')
    const securityToken = ref('')
    const expiration = ref('')

    const BUCKET = 'gs1helper-15088132407'
    const REGION = 'oss-cn-guangzhou'

    const clean = () => {
      accessKeyId.value = ''
      accessKeySecret.value = ''
      securityToken.value = ''
      expiration.value = ''
    }

    /**
     * 判断临时凭证是否到期。
     **/
    const isCredentialsExpired = () => {
      if (expiration.value === '') return true

      const expireDate = new Date(expiration.value)
      const now = new Date()
      // 如果有效期不足一分钟，视为过期。
      const expired = expireDate.getTime() - now.getTime() <= 60000
      console.log('expired', expired)
      if (expired) clean()
      return expired
    }

    /**
     * 获取临时凭证
     */
    const getCredentials = () => new Promise((resolve, reject) => {
      // console.log('isCredentialsExpired()', isCredentialsExpired())
      if (isCredentialsExpired()) {
        // 过期，重新获取。
        // console.log('cccccccccc')
        getStsTokenForOssApi().then((res) => {
          console.log('res', res)
          if (res.success) {
            accessKeyId.value = res.data.accessKeyId
            accessKeySecret.value = res.data.accessKeySecret
            console.log('res.data.expiration', res.data.expiration)
            expiration.value = res.data.expiration
            console.log('expiration.value', expiration.value)
            securityToken.value = res.data.securityToken
            /*           ossClient.value = new OSS({
              accessKeyId: accessKeyId.value,
              accessKeySecret: accessKeySecret.value,
              bucket: BUCKET,
              region: REGION,
              stsToken: securityToken.value,
            }) */
            resolve(true)
          }
          else {
            console.log('0000')
            reject(false)
          }
        }).catch(() => {
          console.log('1111')
          reject(false)
        })
      }
      else {
        // console.log('2222')
        resolve(true)
      }
    })

    // return了才会存到localStorage
    return {
      BUCKET, REGION,
      accessKeyId, accessKeySecret, securityToken, expiration, getCredentials,
    }
  },
  {
    persist: {
      storage: persistedState.localStorage,
    },
  },
)
