<script setup lang='ts'>
import { MENU } from '~/config/menu'

const route = useRoute()

const menuList = ref(MENU)

const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}

const getAllRoutes = () => {
  const m: string[] = []
  MENU.forEach((item) => {
    m.push(item.label)
  })
  return m
}

const expand = ref(getAllRoutes())
const activePath = computed(() => route.path)
const handleToPath = async (path: string) => {
  await navigateTo({
    path,
  })
}
</script>

<template>
  <div class="f-box rd-2 mt-3 shrink-0 bg-white p-2">
    <el-menu
      :default-openeds="expand"
      @open="handleOpen"
      @close="handleClose"
    >
      <el-sub-menu
        v-for="(item) in menuList"
        :key="item.label"
        :index="item.label"
      >
        <template #title>
          <div class="flex items-center gap-2">
            <div :class="item.icon" />
            {{ item.label }}
          </div>
        </template>
        <el-menu-item
          v-for="subItem in item.items"
          :key="subItem.route"
          :index="subItem.route"
          @click="handleToPath(subItem.route)"
        >
          <div
            :class="activePath === subItem.route ? 'color-blue':''"
          >
            {{ subItem.label }}
          </div>
        </el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<style scoped lang='scss'>
.f-box {
  width: var(--cf-left-menu-width);
  max-width: var(--cf-left-menu-width);
  //border-radius: 0 0 0.5rem 0.5rem;
}
</style>
