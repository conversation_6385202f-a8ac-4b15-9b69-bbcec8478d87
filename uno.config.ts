import { defineConfig, presetIcons, presetUno, transformerDirectives } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetIcons({
      // warn: true,
      // prefix: ["i-"],
      // extraProperties: {
      //   display: "inline-block",
      // },
    }),
  ],
  safelist: [
    'i-ri:upload-cloud-2-line',
    'i-ri:qr-code-line',
  ],
  theme: {
    colors: {
      blue: '#165DFF', // 定义蓝色 --el-color-primary
      red: '#DC3126', // --el-color-danger
      green: '#21C33B', // --el-color-success
      yellow: '#F79F23', // --el-color-warning
      gray: '#909399', // --el-color-info
      // 其他颜色...
    },
  },
  transformers: [
    transformerDirectives(),
  ],
})
