<script lang="ts" setup>
import { initializeKinesis } from '@amineyarman/kinesis'
import { useArticleLoadData } from '~/composables/apiArticle'

onMounted(() => {
  if (import.meta.client) {
    initializeKinesis()
  }
})
const { id } = useRoute().params

const { data, status, error, refresh, clear } = await useArticleLoadData({
  articleId: Number(id),
})

const context = computed(() => {
  try {
    return updateVideoDimensions(data.value.articleConent)
  }
  catch (e) {
    return ''
  }
})

const updateVideoDimensions = (articleContent: string) => {
  // 正则表达式用于查找 <video> 标签，并捕获 width 和 height 属性
  const videoRegex = /<video[^>]*width="[^"]*"[^>]*height="[^"]*"[^>]*>/gi

  // 替换函数，它会将匹配到的 <video> 标签中的 width 和 height 更新为目标值
  const replaceFn = (match: string) => {
    return match
      .replace(/width="[^"]*"/, 'width="100%"')
      .replace(/height="[^"]*"/, 'height="400px"')
  }

  // 使用正则表达式和替换函数来更新 articleContent 中的所有 <video> 标签
  return articleContent.replace(videoRegex, replaceFn)
}
</script>

<template>
  <div class="w-full">
    <div
      v-if="!data.imageUrl"
      class="f-tile_1 w-full"
      data-kinesistransformer
    >
      <div
        data-kinesistransformer-element
        data-ks-strength="10"
        data-ks-transform="translate"
      >
        <NuxtImg
          alt="条码规范"
          class="f-font-standard"
          height="59"
          src="/resource/images/tile/tile_standard.png"
          width="192"
        />
      </div>
      <div
        data-kinesistransformer-element
        data-ks-strength="30"
        data-ks-transform="translate"
      >
        <NuxtImg
          alt="知识学习"
          class="f-img-study"
          height="308"
          src="/resource/images/tile/tile_study.png"
          width="314"
        />
      </div>
    </div>
    <div class="max-w-640px px-10 mx-auto">
      <div
        v-if="data.imageUrl"
        class="f-img-box w-full overflow-hidden o-flex-c"
      >
        <NuxtImg
          :src="data.imageUrl"
          alt=""
        />
      </div>
      <div class="p-10 mt-6 text-center text-2xl font-bold">
        {{ data.articleTitle }}
      </div>
      <el-divider />
      <div
        class="f-html font-size-16px line-height-relaxed pb-10"
        v-html="context"
      />
      <div class="w-full mx-auto">
        <el-divider>
          ·
        </el-divider>
        <NuxtImg
          alt="logo"
          class="m-auto block pt-6 pb-20"
          src="/resource/images/logo_big.png"
          width="120"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.f-tile_1 {
  @apply overflow-hidden relative;

  height: 155px;
  background-size: 100% 100%;
  background-image: url('/resource/images/tile/tile_bg_stydyStandard.png');

  img {
    @apply absolute;
    transform: translateX(-50%);

  }
}

.f-font-standard {
  top: 16px;
  left: 35%;
}

.f-img-study {
  top: 5px;
  left: 60%;
}

.f-img-box {
  height: 200px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.f-html {
  ::v-deep(img) {
    width: 100%;
  }

  ::v-deep(.ace-line) {
    @apply py-4;
  }
}
</style>
