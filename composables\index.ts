import type OSS from 'ali-oss'
import { userStore } from '~/stores/userStore'

/* export const getPostData = async (key: string, path: string, params: any, transform?: any) => {
  // console.log('apiBaseUrl:', useRuntimeConfig().public.apiBaseUrl)
  return useAsyncData(
    key,
    () => $fetch(useRuntimeConfig().public.apiBaseUrl + path, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: params,
    }), {
      transform,
    },
  )
} */

/* export const getGetData = async (key: string, path: string, params: string, transform?: any) => {
  // console.log('apiBaseUrl:', useRuntimeConfig().public.apiBaseUrl)
  return useAsyncData(
    key,
    () => $fetch(useRuntimeConfig().public.apiBaseUrl + path + params, {
      method: 'GET',
    }), {
      transform,
    },
  )
} */

export const useDownloadFile = async (url: string) => {
  try {
    // 不要用Nuxt的$fetch
    const response = await fetch(useRuntimeConfig().public.apiBaseUrl + url, {
      method: 'GET', // 请求方法
      headers: {
        Authorization: userStore().getToken(), // 添加Bearer token
      },
    })

    // console.log(response)
    // console.log(response.headers.get('Content-Disposition'))
    // 创建 Blob 对象
    const blob = await response.blob() // 在这里读取 Response 对象

    // 获取文件名
    const contentDisposition = response.headers.get('Content-Disposition')
    let fileName = '下载'
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename=["']?([^"'\r\n]+)["']?/)
      if (filenameMatch && filenameMatch[1]) {
        fileName = decodeURIComponent(filenameMatch[1].trim())
      }
    }
    // 创建下载链接
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  catch (error) {
    console.error('下载文件时发生错误:', error)
  }
}

/**
 * 下载oss文件
 * @param ossClient
 * @param filePath
 * @param fileName
 */
export const downloadOssFile = (ossClient: OSS, filePath: string, fileName: string) => {
  const response = {
    'content-disposition': `attachment; filename=${encodeURIComponent(
      fileName,
    )}`,
  }
  const url = ossClient.signatureUrl(filePath, { response })
  // 创建下载链接
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
