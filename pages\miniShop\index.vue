<script setup lang='ts'>
import type { TableInstance } from 'element-plus'
import OSS from 'ali-oss'
import { useEasyLightbox } from 'vue-easy-lightbox'
import {
  changeGoodsStateApi,
  goodsByteQrCodeImageApi,
  goodsDelApi,
  goodsPageApi,
  type GoodsPageResData,
} from '~/composables/apiMiniShop'
import { userStore } from '~/stores/userStore'
import { usePagination } from '~/composables/usePagination'
import { goodsEditorStore } from '~/stores/goodsEditorStore'
import { ossStore } from '~/stores/ossStore'
import { ErrorLevel, GoodState } from '~/composables/enum'

const useOssStore = ossStore()
const useGoodsEditorStore = goodsEditorStore()
const { goodId } = storeToRefs(useGoodsEditorStore)
const useUserStore = userStore()
const {
  pageSize,
  pageIndex,
  totalCount,
  tableHeight,
} = usePagination(360, 300)
const loading = computed(() => status.value === 'pending')
const multipleTableRef = ref<TableInstance>()
const multipleSelection = ref<any[]>([])
const ossClient = ref()
const qrImg = ref('')
const upButtonEnabled = ref(false)
const downLoadEnabled = ref(false)
const list = ref<GoodsPageResData[]>([])
const mainImg = ref<string[]>([])
const disableEdit = ref(false)
const qrSetShow = ref(false)
const qrSize = ref(400)
const qrSizeOptions = [{
  label: '300×300',
  value: 300,
}, {
  label: '400×400',
  value: 400,
}, {
  label: '600×600',
  value: 600,
}, {
  label: '800×800',
  value: 800,
}, {
  label: '1000×1000',
  value: 1000,
}]
const errorLevel = ref(ErrorLevel.high)
const errorLevelOptions = ['7%', '15%', '25%', '30%']

const formInline = reactive({
  barCode: '',
  goodsName: '',
})
const formBarCode = ref('')
const formGoodsName = ref('')

const { show, onHide, visibleRef, indexRef, imgsRef } = useEasyLightbox({
  imgs: mainImg.value,
  initIndex: 0,
})

watch(multipleSelection, () => {
  if (multipleSelection.value.length > 0) {
    upButtonEnabled.value = true
    downLoadEnabled.value = true
  }
  else {
    upButtonEnabled.value = false
    downLoadEnabled.value = false
  }
})
const { data, status, error, refresh, clear } = await goodsPageApi({
  barCode: formBarCode,
  brandName: '',
  endDate: '',
  goodsName: formGoodsName,
  // goodsState: null,
  spec: '',
  startDate: '',
  groupBy: '',
  needTotalCount: true,
  orderBy: 'lastUpdatedDate',
  orderDirection: OrderDirection.desc,
  pageIndex: pageIndex,
  pageSize: pageSize,
  userId: Number(useUserStore.userId),
}, {
  immediate: false,
})

onMounted(() => {
  if (import.meta.client) {
    useOssStore.getCredentials().then(() => {
      ossClient.value = new OSS({
        bucket: useOssStore.BUCKET,
        region: useOssStore.REGION,
        accessKeyId: useOssStore.accessKeyId,
        accessKeySecret: useOssStore.accessKeySecret,
        stsToken: useOssStore.securityToken,
      })
      refresh()
    }).catch((err) => {
      ElMessage.error('获取OSS凭证失败')
    })
  }
})

watch([data, ossClient], () => {
  if (import.meta.client && data.value?.data && ossClient.value) {
    list.value = updateImageUrls(data.value.data)
    totalCount.value = data.value.totalCount
  }
}, { immediate: true })

function updateImageUrls(items: GoodsPageResData[]): GoodsPageResData[] {
  const arr: GoodsPageResData[] = []
  items.forEach((item) => {
    let imgUrl = '/resource/images/shop/mainImg_noImg.png'
    if (item.imageUrl !== null) {
      try {
        // imgUrl = replaceDomain(ossClient.value.signatureUrl(item.imageUrl))
        imgUrl = ossClient.value.signatureUrl(item.imageUrl)
      }
      catch (e) {
      }
    }
    arr.push({
      ...item,
      imageUrl: imgUrl,
    })
  })
  return arr
}

const handleSearch = () => {
  formBarCode.value = formInline.barCode
  formGoodsName.value = formInline.goodsName
}
const handleReset = () => {
  pageIndex.value = 1
  formInline.barCode = ''
  formInline.goodsName = ''
  formBarCode.value = ''
  formGoodsName.value = ''
}

const handleCreate = async () => {
  await navigateTo({
    path: '/miniShop/create',
  })
}

const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val
}

const handleDel = (id: number) => {
  goodsDelApi({ goodsId: id }).then(() => {
    ElMessage.success('删除成功')
    refresh()
  })
}

const goodsStateChange = (state: number, goodsId: number) => {
  // console.log('state', state)
  changeGoodsStateApi({
    goodsIds: [goodsId],
    goodsState: state,
  }).then(() => {
    refresh()
  })
}

const handleUpDown = (state: number) => {
  // console.log(multipleSelection.value);
  const arr = multipleSelection.value.map(item => item.goodsId)
  changeGoodsStateApi({
    goodsIds: arr,
    goodsState: state,
  }).then(() => {
    refresh()
  })
}

const showQr = async (id: number) => {
  goodsByteQrCodeImageApi({
    errorLevel: ErrorLevel.high,
    goodsId: id,
    height: qrSize.value,
    width: qrSize.value,
  }).then(async (res: any) => {
    // console.log('res', res)
    const reader = res.getReader() // 获取 ReadableStream 的 reader
    const chunks = [] // 存储读取的块

    let result
    while (!(result = await reader.read()).done) {
      chunks.push(result.value) // 将每个块推入数组
    }

    // 创建 Blob 对象并生成 URL
    const blob = new Blob(chunks, { type: 'image/png' }) // 根据实际类型调整
    qrImg.value = URL.createObjectURL(blob) // 创建对象 URL
  })
}

const handleDownQr = (name: string, url: string) => {
  // useDownloadFile(url)
  // 创建下载链接
  const link = document.createElement('a')
  link.href = url
  link.download = name + '.png'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const editorRef = ref()
const dialogVisible = ref(false)
const handleEditor = (id: number, goodsState: GoodState) => {
  if ([GoodState.up, GoodState.violation].includes(goodsState)) {
    ElMessage.warning('只有下架或审核中的商品才可编辑')
    disableEdit.value = true
  }
  goodId.value = id
  dialogVisible.value = true
}

const handleSave = () => {
  if (!disableEdit.value) {
    editorRef.value.submitForm()
  }
}

const handleCancel = () => {
  dialogVisible.value = false
}

const handleMainPreview = (imgUrl: string) => {
  mainImg.value = [imgUrl]
  show()
}

const handleSetQr = () => {
  qrSetShow.value = true
}
</script>

<template>
  <div class="w-full o-bg-blue-light">
    <AppMenuAndContent>
      <el-form
        :inline="true"
        :model="formInline"
      >
        <el-form-item label="商品条码">
          <el-input
            v-model="formInline.barCode"
            style="width: 180px"
            clearable
            @keydown.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="产品名称">
          <el-input
            v-model="formInline.goodsName"
            style="width: 180px"
            clearable
            @keydown.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleSearch"
          >
            <div class="i-ri:search-line mr-2" />
            查询
          </el-button>
          <el-button
            @click="handleReset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <div class="flex pb-3 ">
        <el-button @click="handleCreate">
          <div class="i-ri:add-large-line mr-1" />
          新增商品
        </el-button>
        <el-button
          :disabled="!upButtonEnabled"
          @click="handleUpDown(1)"
        >
          批量上架
        </el-button>
        <el-button
          :disabled="!upButtonEnabled"
          @click="handleUpDown(-1)"
        >
          批量下架
        </el-button>
        <!--        <el-button
          :disabled="!downLoadEnabled"
        >
          批量下载二维码
        </el-button> -->
      </div>
      <ClientOnly>
        <el-table
          ref="multipleTableRef"
          v-loading="loading"
          :data="list"
          :height="tableHeight"
          style="width: 100%"
          row-key="goodsId"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
          />
          <el-table-column
            prop="imageUrl"
            label="主图"
            width="90"
          >
            <template #default="scope">
              <el-image
                class="cursor-pointer"
                alt="主图"
                style="width: 60px; height: 60px"
                :src="scope.row.imageUrl"
                fit="cover"
                lazy
                loading="lazy"
                @click="handleMainPreview(scope.row.imageUrl)"
              >
                <template #error>
                  <NuxtImg
                    class="rd-1"
                    src="/resource/images/shop/mainImg_noImg.png"
                    width="60"
                    height="60"
                    alt="主图"
                  />
                </template>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column
            prop="barCode"
            label="商品条码"
            width="160"
          />
          <el-table-column
            prop="goodsName"
            label="产品名称"
          >
            <template #default="scope">
              <div
                class="o-inline-2"
              >
                {{ scope.row.goodsName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="brandName"
            label="品牌名称"
          />
          <el-table-column
            prop="spec"
            label="规格"
            width="100"
          />
          <el-table-column
            prop="goodsState"
            label="状态"
            width="180"
          >
            <template #default="scope">
              <el-radio-group
                v-if="[1, -1].includes(scope.row.goodsState)"
                v-model="scope.row.goodsState"
                size="small"
                @change="goodsStateChange(scope.row.goodsState, scope.row.goodsId)"
              >
                <el-radio-button
                  label="上架"
                  :value="1"
                />
                <el-radio-button
                  label="下架"
                  :value="-1"
                />
              </el-radio-group>
              <el-tag
                v-if="scope.row.goodsState === 0"
                type="warning"
                effect="plain"
              >
                审核中
              </el-tag>
              <div v-if="scope.row.goodsState === -2">
                <el-tag
                  type="danger"
                  effect="plain"
                >
                  违规
                </el-tag>
                <div class="color-gray text-sm mt-2">
                  <span class="color-red">*</span>
                  {{ scope.row.stateDesc }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="qrCode"
            width="200"
          >
            <template #default="scope">
              <div class="flex gap-4">
                <el-popover
                  :width="230"
                  placement="top"
                  trigger="click"
                >
                  <template #reference>
                    <el-link
                      type="primary"
                      @click="showQr(scope.row.goodsId)"
                    >
                      预览 / 二维码
                    </el-link>
                  </template>
                  <template #default>
                    <div class="o-flex-c flex-col">
                      <div class="f-qr o-flex-c overflow-hidden">
                        <el-image
                          class="f-qr-img"
                          :src="qrImg"
                          fit="cover"
                          alt="二维码"
                        />
                      </div>
                      <el-button
                        link
                        class="flex gap-1  mb-2 items-center justify-center"
                        @click="handleSetQr"
                      >
                        {{ errorLevel }}容错，{{ qrSize }}×{{ qrSize }}px
                        <div class="i-ri:settings-4-line text-lg pl-2" />
                      </el-button>
                      <div class="o-flex-c gap-4">
                        <div class="text-sm">
                          手机扫码预览
                        </div>
                        <el-button
                          size="small"
                          @click="handleDownQr(
                            scope.row.barCode + '_' + scope.row.goodsName, qrImg,
                          )"
                        >
                          下载二维码
                        </el-button>
                      </div>
                    </div>
                  </template>
                </el-popover>
                <el-link
                  type="primary"
                  @click="handleEditor(scope.row.goodsId, scope.row.goodsState)"
                >
                  编辑
                </el-link>
                <el-popconfirm
                  title="是否确认删除？"
                  @confirm="handleDel(scope.row.goodsId)"
                >
                  <template #reference>
                    <el-link
                      type="danger"
                    >
                      删除
                    </el-link>
                  </template>
                </el-popconfirm>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </ClientOnly>
      <el-pagination
        v-model:current-page="pageIndex"
        v-model:page-size="pageSize"
        :page-sizes="[20, 50, 100]"
        :total="totalCount"
        class="float-right mt-8"
        layout="total, sizes, prev, pager, next, jumper"
      />
    </AppMenuAndContent>
    <ClientOnly>
      <el-dialog
        v-model="dialogVisible"
        :title="disableEdit ? '编辑（只有下架或审核中的商品才可编辑）': '编辑' "
        width="700"
        :close-on-click-modal="false"
        :lock-scroll="false"
      >
        <ShopEditor
          ref="editorRef"
          v-model:dialog-visible="dialogVisible"
          :disable-edit="disableEdit"
          :is-create="false"
          @success="refresh"
        />
        <template #footer>
          <div>
            <el-button @click="handleCancel">
              取消
            </el-button>
            <el-button
              type="primary"
              :disabled="disableEdit"
              @click="handleSave"
            >
              保存
            </el-button>
          </div>
        </template>
      </el-dialog>
    </ClientOnly>
    <el-dialog
      v-model="qrSetShow"
      title="二维码设置"
      width="500"
      :lock-scroll="false"
    >
      <el-form
        label-width="auto"
        style="max-width: 480px"
      >
        <el-form-item label="容错率">
          <el-radio-group v-model="qrSize">
            <el-radio
              v-for="item in qrSizeOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="尺寸">
          <el-radio-group v-model="errorLevel">
            <el-radio
              v-for="item in errorLevelOptions"
              :key="item"
              :value="item"
            >
              {{ item }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-dialog>
    <vue-easy-lightbox
      :imgs="mainImg"
      :index="indexRef"
      :visible="visibleRef"
      @hide="onHide"
    />
  </div>
</template>

<style scoped lang='scss'>
.f-qr {
  $w: 200px;
  $aw: 10px;
  width: $w;
  height: $w;

  .f-qr-img {
    width: calc($w + $aw);
    height: calc($w + $aw);
  }
}
</style>
