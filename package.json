{"name": "barcode_mini-site", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "dev:ssl": "set NODE TLS REJECT UNAUTHORIZED=0&&npx nuxi dev --https --ssl-cert devcert.pem --ssl-key devcert.key", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "gpcToTs": "node build/gpcJsonToData.cjs"}, "dependencies": {"@amineyarman/kinesis": "^1.0.6", "@element-plus/icons-vue": "^2.3.1", "@nuxt/image": "^1.10.0", "@pinia/nuxt": "^0.11.1", "@tinymce/tinymce-vue": "^6.2.0", "@types/uuid": "^10.0.0", "@yeger/vue-masonry-wall": "^5.0.21", "ali-oss": "^6.23.0", "bowser-ultralight": "^1.0.6", "dayjs": "^1.11.13", "element-plus": "^2.10.4", "is-mobile": "^5.0.0", "lodash-unified": "^1.0.3", "nuxt": "^3.17.6", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "swiper": "^11.2.10", "throttle-debounce": "^5.0.2", "ufo": "^1.6.1", "uuid": "^11.1.0", "vue": "^3.5.17", "vue-confetti-explosion": "^1.0.2", "vue-easy-lightbox": "^1.19.0", "vue-router": "^4.5.1"}, "packageManager": "pnpm@9.0.5+sha1.6db99351548f394a1d96aa1de98dec032aef8823", "devDependencies": {"@element-plus/nuxt": "^1.1.3", "@iconify-json/ri": "^1.2.5", "@nuxt/eslint": "^1.5.2", "@types/ali-oss": "^6.16.11", "@unocss/nuxt": "^66.3.3", "@unocss/preset-icons": "^66.3.3", "@vueuse/core": "^13.5.0", "@vueuse/nuxt": "^13.5.0", "eslint": "^9.31.0", "fontmin": "^2.0.2", "prettier": "^3.6.2", "sass": "^1.89.2", "typescript": "^5.8.3", "unocss": "^66.3.3"}}