<script setup lang='ts'>
import ConfettiExplosion from 'vue-confetti-explosion'

const useStepStore = miniShopCreateStepStore()
const { activeStep } = storeToRefs(useStepStore)
const confettiVisible = ref(false)

onMounted(() => {
  if (import.meta.client) {
    confetti()
  }
})

const confetti = async () => {
  confettiVisible.value = false
  await nextTick()
  confettiVisible.value = true
}

const toGoodList = async () => {
  await navigateTo({
    path: '/miniShop',
  })
}

onUnmounted(() => {
  activeStep.value = 0
})
</script>

<template>
  <div
    class="f-h o-flex-c flex-col py-20"
  >
    <div
      class="text-2xl pl-8"
      style="margin-top:-20%;"
    >
      商品添加成功！🎉
    </div>
    <ConfettiExplosion v-if="confettiVisible" />
    <el-button
      class="mt-8"
      @click="toGoodList"
    >
      前往商品列表
    </el-button>
  </div>
</template>

<style scoped lang='scss'>
.f-h{
  height: max(calc(100vh - 727px),300px);
}
</style>
