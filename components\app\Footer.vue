<script lang="ts" setup>
</script>

<template>
  <div
    class="p-10 o-bg-blue-light"
  >
    <div class="max-w-1536px sm:px-10 md:px-20 flex items-end gap-4 justify-end sm:justify-between mx-auto">
      <div class="color-gray hidden sm:block">
        <div class="font-bold o-font-default-color mb-6">
          相关协议
        </div>
        <div class="flex flex-col gap-3 text-sm">
          <NuxtLink to="/protocol/78">
            用户隐私协议
          </NuxtLink>
          <NuxtLink to="/protocol/77">
            平台免责声明
          </NuxtLink>
          <NuxtLink to="/protocol/75">
            付费免责声明（条码胶片）
          </NuxtLink>
          <NuxtLink to="/protocol/74">
            付费免责声明（信息通报）
          </NuxtLink>
          <NuxtLink to="/protocol/76">
            付款免责声明（设计）
          </NuxtLink>
          <a
            href="https://beian.miit.gov.cn/#/Integrated/index"
            target="_blank"
          >粤ICP备17080421号-2</a>
        </div>
      </div>
      <div class="flex flex-col items-end o-font-default-color">
        <div class="flex gap-12">
          <div class="f-qr-box text-right">
            <div>联系客服</div>
            <div>请微信扫码</div>
            <NuxtImg
              src="/resource/images/wx_customer_service.png"
              alt="联系客服"
            />
          </div>
          <div class="f-qr-box text-right">
            <div>微信公众号</div>
            <div>条码帮</div>
            <NuxtImg
              src="/resource/images/qrcode_for_gh.png"
              alt="联系客服"
            />
          </div>
        </div>
        <div class="text-right pr-3 sm:mt-6">
          2024 © All rights reserved. Made by 条码帮
        </div>
        <!--        <div class="text-right pr-3 mt-6">
        2024 © All rights reserved. Made by 条码帮服务有限公司
      </div> -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.f-qr-box {
  $w: 134px;
  width: $w;

  div {
    @apply pr-3 py-1;
  }

  img {
    width: $w;
    height: $w;
  }

}
</style>
