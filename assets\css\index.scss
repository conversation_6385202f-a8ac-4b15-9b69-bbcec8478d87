@use "resetSwiper.scss" as *;
@use "elementPlus.scss" as *;
@use "resetTinyMce.scss" as *;

:root,
html,
body {
  --cf-left-menu-width: 12rem;

  margin: 0;
  padding: 0;
  font-size: 14px;
  overflow: auto;
  color: var(--el-text-color-primary);
}

::-webkit-scrollbar {
  $w: 8px;

  width: $w;
  height: $w;
  background-color: $vf-bg-light-primary;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px; /* 圆角 */
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

a {
  text-decoration: none;
  color: inherit;
}

.o-flex-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

.o-font-default-color {
  color: var(--el-text-color-regular);
}

.o-font-color-secondary{
  color: var(--el-text-color-secondary);
}

.o-color-default-black{
  color: $vf-default-black;
}

.o-bg-content {
  background-color: var(--el-text-color-regular);
}

.o-bg-gray {
  background: $vf-default-color-gray;
}

.o-bg-gray-light {
  background: $vf-default-bg-gray;
}

.o-bg-blue-light {
  background: $vf-bg-light-primary;
}

.o-bg-default-black{
  background: $vf-default-black;
}

.o-inline-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.o-inline-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.o-inline-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.o-transition-fast{
  transition: all 200ms ease-in-out;
}

.o-border{
  @apply rd-2 px-4 pb-4 pt-6;

  border:1px solid var(--el-border-color);
}

.o-p{
  text-indent: 2em;
}

.o-shadow {
  box-shadow: var(--el-box-shadow);
}

.o-shadow-light {
  box-shadow: var(--el-box-shadow-light);
}

.o-loading{
  animation: of-loading-rotate 1s linear infinite;
}

@keyframes of-loading-rotate {
  from {
    transform: rotate(0deg); /* 从0度开始 */
  }
  to {
    transform: rotate(360deg); /* 旋转到360度 */
  }
}

//-------------------------------------------------------------------

.of-t-1 {
  font-size: 3.293vw;
  font-weight: bold;
}

.of-t-2 {
  font-size: 3.293vw;
  margin-top: -1vw;
}

.of-t-3 {
  font-size: 8.8vw;
  font-weight: bold;
  margin-top: -1vw;
}

//-------------------------------------------------------------------

.vel-img {
  box-shadow: none !important;
}

.vel-modal {
  backdrop-filter: blur(5px);
}
