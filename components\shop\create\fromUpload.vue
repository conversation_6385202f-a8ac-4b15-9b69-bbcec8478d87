<script setup lang='ts'>
import { genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'

const useStepStore = miniShopCreateStepStore()
const { activeStep, createType } = storeToRefs(useStepStore)
const upload = ref<UploadInstance>()

const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const submitUpload = () => {
  upload.value!.submit()
}
const handleDownLoad = () => {
  useDownloadFile(downloadExcelTempPath)
}

const uploadSuccess = () => {
  ElMessage.success('上传成功')
  activeStep.value = 3
}

const uploadFailed = () => {
  ElMessage.error('上传失败')
}
</script>

<template>
  <div class="f-h">
    <div class="font-bold text-lg">
      批量上传步骤：
    </div>
    <div class="grid gap-4  mt-4">
      <MyStepBox
        class="flex-1"
        step="1"
        text="批量上传之前，请先点击“数据批量导入模板下载”按钮，下载“数据批量导入模板”。"
      >
        <el-button @click="handleDownLoad">
          <div class="i-ri:download-2-line mr-1" />
          下载模版
        </el-button>
      </MyStepBox>
      <MyStepBox
        class="flex-1"
        step="2"
        text="根据模板要求，填写数据模板内容。"
      />
      <MyStepBox
        class="flex-1"
        step="3"
        text="点击“数据批量导入”按钮，将已填写数据内容的模板上传至平台。"
      >
        <el-upload
          ref="upload"
          accept=".xls,.xlsx"
          :action="useRuntimeConfig().public.apiBaseUrl + ImportGoodsPath"
          :limit="1"
          :on-exceed="handleExceed"
          :auto-upload="false"
          :on-error="uploadFailed"
          :on-success="uploadSuccess"
          :data="{
            userId: userStore().userId,
          }"
          name="file"
          :headers="{
            Authorization: userStore().getToken(),
          }"
        >
          <template #trigger>
            <el-button type="primary">
              选择文件
            </el-button>
          </template>
          <el-button
            class="ml-3"
            type="success"
            @click="submitUpload"
          >
            上传
          </el-button>
        </el-upload>
      </MyStepBox>
    </div>
  </div>
</template>

<style scoped lang='scss'>
.grid {
    grid-template-columns: 1fr 1fr 2fr;
}

@media (max-width: 1024px) {
    .grid {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr 1fr;
    }
}
.f-h{
  height: max(calc(100vh - 587px),300px);
}
</style>
