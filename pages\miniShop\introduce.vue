<script setup lang="ts">
import { initializeKinesis } from '@amineyarman/kinesis'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { FreeMode, Navigation, Thumbs } from 'swiper/modules'
// import 'swiper/css'
// import 'swiper/css/free-mode'
// import 'swiper/css/navigation'
// import 'swiper/css/thumbs'
import { useEasyLightbox } from 'vue-easy-lightbox'

const thumbsSwiper = ref(null)
const sliderImagesList = [
  '/resource/images/shop/miniShop_01.jpg',
  '/resource/images/shop/miniShop_02.jpg',
  '/resource/images/shop/miniShop_03.jpg',
  '/resource/images/shop/miniShop_04.jpg',
]
const setThumbsSwiper = (swiper) => {
  thumbsSwiper.value = swiper
}
const modules = [FreeMode, Navigation, Thumbs]

onMounted(() => {
  if (import.meta.client) {
    initializeKinesis()
  }
})

const { show, onHide, visibleRef, indexRef, imgsRef } = useEasyLightbox({
  imgs: sliderImagesList,
  initIndex: 0,
})
</script>

<template>
  <div class="w-full o-bg-blue-light">
    <AppMenuAndContent>
      <div
        class="f-box w-full flex"
        data-kinesistransformer
      >
        <div
          data-kinesistransformer-element
          data-ks-strength="20"
          data-ks-transform="translate"
          class="ml--16"
        >
          <NuxtImg
            src="/resource/images/shop/iphone_miniShop.png"
            alt="phone"
            width="500"
            height="800"
            class="f-phone"
          />
        </div>
        <div
          class="mt-6"
        >
          <div class="text-4xl font-bold mb-4">
            二维码微站
          </div>
          <ClientOnly
            fallback-tag="div"
          >
            <template #fallback>
              <div class="f-swiper-big o-flex-c">
                <CircleLoader />
              </div>
            </template>
            <swiper
              :space-between="0"
              :thumbs="{ swiper: thumbsSwiper }"
              :modules="modules"
              class="f-swiper-big overflow-hidden rd-2"
            >
              <swiper-slide
                v-for="(item, index) in sliderImagesList"
                :key="index"
                @click="show(index)"
              >
                <NuxtImg
                  :src="item"
                />
              </swiper-slide>
            </swiper>
          </ClientOnly>
          <ClientOnly
            fallback-tag="div"
            class="f-swiper-small"
          >
            <swiper
              :space-between="10"
              :slides-per-view="5"
              :free-mode="true"
              :watch-slides-progress="true"
              :modules="modules"
              class="f-swiper-small"
              @swiper="setThumbsSwiper"
            >
              <swiper-slide
                v-for="(item, index) in sliderImagesList"
                :key="index"
              >
                <NuxtImg
                  :src="item"
                />
              </swiper-slide>
            </swiper>
          </ClientOnly>
          <div class="pt-10 font-bold">
            手机扫码查看示例展示：
          </div>
          <NuxtImg
            class="ml--9"
            width="300"
            height="300"
            src="https://wx.gs1helper.com/images/miniShop_Qr_template.png"
          />
          <a
            class="block w-fit px-9 py-4 rd-2 bg-blue color-white"
            href="https://wx.gs1helper.com/12"
            target="_blank"
          >微信扫码前往开通</a>
          <NuxtImg
            src="/resource/images/makeFilm/appCode_from_websiteWxQr.png"
            alt="QR code"
            width="160"
            height="160"
            class="mt-8 block"
          />
        </div>
      </div>
    </AppMenuAndContent>
    <vue-easy-lightbox
      :imgs="sliderImagesList"
      :index="indexRef"
      :visible="visibleRef"
      @hide="onHide"
    />
  </div>
</template>

<style scoped lang="scss">
.f-box{
  //height: max(calc(100vh - 490px),1000px)
}

.f-phone{
  filter: drop-shadow(0px 14px 20px rgba(22, 93, 255, 0.2));
}

.f-swiper-big {
  $w: 360px;
  height: $w;
  width: $w;
}

.f-swiper-small {
  @apply mt-2;

  $sw: 50px;

  width: calc($sw * 5 + 10px * 4);
  height: $sw;
  box-sizing: border-box;

  .swiper-slide {
    width: $sw;
    height: $sw;
    opacity: 0.4;
  }
}

.f-swiper-small .swiper-slide-thumb-active {
  opacity: 1;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
