.el-form-item__label {
  //font-weight: 700;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

.el-dropdown-menu {
  padding: 0 !important;
}

.el-range-separator {
  box-sizing: content-box;
}

.is-dark {
  z-index: 9999 !important;
}

/* 重置 el-button 中 icon 的 margin */
.reset-margin [class*="el-icon"] + span {
  margin-left: 2px !important;
}

/* 自定义 popover 的类名 */
.pure-popper {
  padding: 0 !important;
}

/* 自定义 tooltip 的类名 */
.pure-tooltip {
  // 右侧操作面板right-panel类名的z-index为40000，tooltip需要大于它才能显示
  z-index: 41000 !important;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

.pure-dialog {
  .pure-dialog-svg {
    color: var(--el-color-info);
  }

    .el-dialog__header.show-close {
    padding-right: 16px;
  }

  .el-dialog__headerbtn {
    top: 20px;
    right: 14px;
    width: 24px;
    height: 24px;
  }

  .pure-dialog-svg {
    color: var(--el-color-info);
  }

  .el-dialog__footer {
    padding-top: 0;
  }
}

/* 全局覆盖element-plus的el-tour、el-dialog、el-drawer、el-message-box、el-notification组件右上角关闭图标和el-upload上传文件列表右侧关闭图标的样式，表现更鲜明 */
.el-dialog__headerbtn,
.el-message-box__headerbtn {
  &:hover {
    .el-dialog__close {
      color: var(--el-color-info) !important;
    }
  }
}

.el-icon {
  &.el-tour__close,
  &.el-dialog__close,
  &.el-drawer__close,
  &.el-message-box__close,
  &.el-notification__closeBtn,
  .el-upload-list__item.is-ready &.el-icon--close {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    outline: none;
    transition:
      background-color 0.2s,
      color 0.2s;

    &:hover {
      color: rgb(0 0 0 / 88%) !important;
      text-decoration: none;
      background-color: rgb(0 0 0 / 6%);

      .pure-dialog-svg {
        color: rgb(0 0 0 / 88%) !important;
      }
    }
  }
}

/* 克隆并自定义 ElMessage 样式，不会影响 ElMessage 原本样式，在 src/utils/message.ts 中调用自定义样式 ElMessage 方法即可，整体暗色风格在 src/style/dark.scss 文件进行了适配 */
.pure-message {
  padding: 10px 13px !important;
  background: #fff !important;
  border-width: 0 !important;
  box-shadow:
    0 3px 6px -4px #0000001f,
    0 6px 16px #00000014,
    0 9px 28px 8px #0000000d !important;

  &.el-message.is-closable .el-message__content {
    padding-right: 17px !important;
  }

  & .el-message__content {
    color: #000000d9 !important;
    pointer-events: all !important;
    background-image: initial !important;
  }

  & .el-message__icon {
    margin-right: 8px !important;
  }

  & .el-message__closeBtn {
    right: 9px !important;
    border-radius: 4px;
    outline: none;
    transition:
      background-color 0.2s,
      color 0.2s;

    &:hover {
      background-color: rgb(0 0 0 / 6%);
    }
  }
}

/* 自定义菜单搜索样式 */
.pure-search-dialog {
  @media screen and (width > 760px) and (width <= 940px) {
    .el-input__inner {
      font-size: 12px;
    }
  }

  @media screen and (width <= 470px) {
    .el-input__inner {
      font-size: 12px;
    }
  }

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding-top: 12px;
    padding-bottom: 0;
  }

  .el-input__inner {
    font-size: 1.2em;
  }

  .el-dialog__footer {
    padding-bottom: 10px;
    box-shadow:
      0 -1px 0 0 #e0e3e8,
      0 -3px 6px 0 rgb(69 98 155 / 12%);
  }
}

/* 仿 el-scrollbar 滚动条样式，支持大多数浏览器，如Chrome、Edge、Firefox、Safari等。整体暗色风格在 src/style/dark.scss 文件进行了适配 */
.pure-scrollbar {
  /* Firefox */
  scrollbar-width: thin; /* 可选值为 'auto', 'thin', 'none' */
  scrollbar-color: rgb(221 222 224) transparent; /* 滑块颜色、轨道颜色 */
  ::-webkit-scrollbar {
    width: 6px; /* 滚动条宽度 */
  }

  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: transparent; /* 轨道颜色 */
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background-color: rgb(221 222 224);
    border-radius: 4px;
  }

  /* 滚动条滑块：hover状态 */
  ::-webkit-scrollbar-thumb:hover {
    background: rgb(199 201 203); /* 滑块hover颜色 */
  }
}


.el-dialog {
  //border-radius: $o-radius-lg;

}

.el-dialog,
.sidebar-container .is-active.submenu-title-noDropdown.outer-most::before{
  //border-radius: $o-radius !important;
}

.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: #e4deff;
}

.el-popper.is-customized .el-popper__arrow::before {
  background: #e4deff;
  right: 0;
}

.el-menu{
  border-right: none!important;
}

//Vxe--------------------------

.vxe-input--inner,
.vxe-button.type--button:not(.is--round),
.vxe-table--render-default.is--round:not(.is--header):not(.is--footer) .vxe-table--body-wrapper.body--wrapper, .vxe-table--render-default.is--round .vxe-table--border-line, .vxe-table--render-default.is--round .vxe-table--render-default.is--round {
  border-radius: 6px!important;
}

.vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.body--wrapper, .vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.body--wrapper {
  border-radius: 6px 6px 0 0!important;
}
.vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.fixed-left--wrapper {
  border-radius: 6px 0 0 0!important;
}
.vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.fixed-right--wrapper {
  border-radius: 0 6px 0 0!important;
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.body--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.body--wrapper {
  border-radius: 0 0 6px 6px!important;
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.fixed-left--wrapper, .vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.fixed-left--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.fixed-left--wrapper {
  border-radius: 0 0 0 6px!important;
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.fixed-right--wrapper, .vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.fixed-right--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.fixed-right--wrapper {
  border-radius: 0 0 6px 0!important;
}
.vxe-loading>.vxe-loading--chunk,
.vxe-loading>.vxe-loading--warpper{
  color:var(--el-color-primary) !important;
}
