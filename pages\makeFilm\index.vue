<script setup lang="ts">
import { initializeKinesis } from '@amineyarman/kinesis'

onMounted(() => {
  if (import.meta.client) {
    initializeKinesis()
  }
})
</script>

<template>
  <div class="w-full o-bg-blue-light">
    <AppMenuAndContent>
      <div
        class="f-box o-flex-c w-full"
        data-kinesistransformer
      >
        <div
          data-kinesistransformer-element
          data-ks-strength="20"
          data-ks-transform="translate"
          class="ml--26"
        >
          <NuxtImg
            class="f-phone"
            src="/resource/images/makeFilm/iphone_makeFilm.png"
            alt="phone"
            width="500"
            height="800"
          />
        </div>
        <div
          class="font-light mt-12"
          data-kinesistransformer-element
          data-ks-strength="10"
          data-ks-transform="translate"
        >
          <div class="text-6xl">
            商品标准条码
          </div>
          <div class="font-bold text-6xl mt-2">
            胶片制作
          </div>
          <div class="text-3xl mt-8 o-font-default-color">
            符合国际 / 国家最新规范
          </div>
          <div class="mt-2 o-font-default-color">
            《GB12904-2008 商品条码 零售商品编码与条码表示》
          </div>
          <div class="mt-1 o-font-default-color">
            《GB/T 16830-2008 商品条码 储运包装商品编码与条码表示》
          </div>
          <a
            class="block w-fit mt-8 px-9 py-4 rd-2 bg-blue color-white"
            href="https://wx.gs1helper.com/12"
            target="_blank"
          >扫码前往微信小程序</a>
          <NuxtImg
            src="/resource/images/makeFilm/appCode_from_websiteWxQr.png"
            alt="QR code"
            width="160"
            height="160"
            class="mt-8 block"
          />
        </div>
      </div>
    </AppMenuAndContent>
  </div>
</template>

<style scoped lang="scss">
.f-box{
    height: max(calc(100vh - 490px),500px)
}

.f-phone{
  filter: drop-shadow(0px 14px 20px rgba(22, 93, 255, 0.2));
}
</style>
