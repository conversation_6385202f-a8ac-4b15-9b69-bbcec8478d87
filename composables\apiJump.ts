import { useFetchApi } from '~/composables/api'

// 参数接口
export interface CreateQrCodeParams {
  dataCode: string
}

// 响应接口
export interface CreateQrCodeRes {
  data: string
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 无需token的二维码接口
 * @param {object} params cmd
 * @param {string} params.dataCode 变量
 * @returns
 */
export const createQrCodeApi = (params: CreateQrCodeParams, options?: any) =>
  useFetchApi<CreateQrCodeRes>('/api/createQrCode', {
    body: params,
    ...options,
  })
