<script setup lang="ts">
import { initializeKinesis } from '@amineyarman/kinesis'

onMounted(() => {
  if (import.meta.client) {
    initializeKinesis()
  }
})
</script>

<template>
  <div class="f-main-pic-box w-full o-flex-c overflow-hidden">
    <div
      class="flex justify-around grow-1 h-full max-w-1536px py-8 px-20"
      data-kinesistransformer
    >
      <div class="h-full flex flex-col justify-center">
        <div
          data-kinesistransformer-element
          data-ks-strength="20"
          data-ks-transform="translate"
        >
          <NuxtLink to="/makeFilm">
            <NuxtImg
              alt="商品标准条码胶片制作"
              class="img1"
              src="/resource/images/mainPic/mainPic_font_1.png"
            />
          </NuxtLink>
        </div>
        <div
          data-kinesistransformer-element
          data-ks-strength="10"
          data-ks-transform="translate"
        >
          <NuxtImg
            alt="符合国际/国家最新规范"
            class="img2 mt-10"
            src="/resource/images/mainPic/mainPic_font_2.png"
          />
        </div>
      </div>
      <div
        data-kinesistransformer-element
        data-ks-strength="30"
        data-ks-transform="translate"
      >
        <NuxtImg
          alt=""
          class="img3"
          src="/resource/images/mainPic/mainPic_codeBox.png"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.f-main-pic-box {
  height: 420px;
  background-image: url("/resource/images/mainPic/mainPic_bg.jpg");
  background-size: 100% 100%;
}

.img1 {
  width: 423px;
  height: 148px;
}

.img2 {
  width: 607px;
  height: 87px;
}

.img3 {
  width: 554px;
  height: 504px;
  margin-top: -5%;
}

@media (max-width: 1200px) {
  $c: 0.7;

  .img1 {
    width: calc(423px * $c);
    height: calc(148px * $c);
  }

  .img2 {
    width: calc(607px * $c);
    height: calc(87px * $c);
  }

  .img3 {
    width: calc(554px * $c);
    height: calc(504px * $c);
    margin-top: 8%;
  }
}
</style>
