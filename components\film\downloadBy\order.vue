<script lang="ts" setup>
import { PayState, ServerType } from '~/composables/enum'
import { usePagination } from '~/composables/usePagination'
import { orderPageApi } from '~/composables/apiFilm'

const list = ref<OrderPageResData[]>([])
const {
  pageSize,
  pageIndex,
  totalCount,
  tableHeight,
} = usePagination(320, 300)

const { data, status, error, refresh, clear } = await orderPageApi({
  payState: PayState.paid,
  // TODO 需要后台调整可以makeFilm和storeCode筛选，否则前端筛选分页每页条数不同
  // serverType: ServerType.makeFilm,
  needTotalCount: true,
  orderBy: 'payDate',
  orderDirection: OrderDirection.desc,
  pageIndex: pageIndex,
  pageSize: pageSize,
  userId: Number(userStore().userId),
})

watch(data, () => {
  if (data.value?.data) {
    list.value = data.value.data.filter(item => [ServerType.makeFilm, ServerType.storeCode].includes(item.serverType))
    totalCount.value = data.value.totalCount
  }
}, { immediate: true })

const loading = computed(() => status.value === 'pending')

const getQuantity = (startBarCode: string, endBarCode?: string) => {
  // 去掉startBarCode最后一位
  const startNum = Number(startBarCode.slice(0, -1))
  if (endBarCode) {
    const endNum = Number(endBarCode.slice(0, -1))
    return endNum - startNum + 1
  }
  else {
    return 1
  }
}

const handleDownLoad = (id: number) => {
  downloadByOrderApi({
    downloadType: 1,
    orderId: id,
    userId: Number(userStore().userId),
  }).then((res: DownloadByOrderRes) => {
    useDownloadFile(
      downloadCodeZipPath + '?orderId=' + id + '&fileName=' + res.data.name + '.zip',
    )
  })
}
</script>

<template>
  <ClientOnly>
    <el-table
      v-loading="loading"
      :data="list"
      :height="tableHeight"
      style="width: 100%"
    >
      <el-table-column
        label="商品条码"
        prop="startBarCode"
      >
        <template #default="scope">
          <div class="flex items-center">
            <div class="px-3 rd-1 o-bg-blue-light">
              {{ scope.row.startBarCode }}
            </div>
            <template v-if="scope.row.endBarCode !== scope.row.startBarCode">
              <div>~</div>
              <div class="px-3 rd-1 o-bg-blue-light">
                {{ scope.row.endBarCode }}
              </div>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="放大系数"
        prop="size"
        width="100"
      />
      <el-table-column
        label="数量"
        prop="endBarCode"
        width="100"
      >
        <template #default="scope">
          {{ getQuantity(scope.row.startBarCode, scope.row.endBarCode) }}
        </template>
      </el-table-column>
      <el-table-column
        label="订单时间"
        prop="payDate"
        width="180"
      />
      <el-table-column
        label="订单编号"
        prop="orderCode"
        width="180"
      />
      <el-table-column
        label=""
        prop="orderId"
      >
        <template #default="scope">
          <el-link
            type="primary"
            @click="handleDownLoad(scope.row.orderId)"
          >
            下载
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="pageIndex"
      v-model:page-size="pageSize"
      :page-sizes="[20, 50, 100]"
      :total="totalCount"
      class="float-right mt-8"
      layout="total, sizes, prev, pager, next, jumper"
    />
  </ClientOnly>
</template>

<style lang="scss" scoped>

</style>
