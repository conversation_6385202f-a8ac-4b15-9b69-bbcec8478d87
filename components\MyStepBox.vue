<script setup lang='ts'>
defineProps<{
  step: string
  text: string
}>()
</script>

<template>
  <div class="o-border">
    <div class="flex gap-2 items-center text-xl font-medium">
      <div>步骤</div>
      <div class="f-o o-flex-c">
        {{ step }}
      </div>
    </div>
    <el-divider />
    <div class="">
      {{ text }}
    </div>
    <div class="o-flex-c mt-3 py-4">
      <slot />
    </div>
  </div>
</template>

<style scoped lang='scss'>
.f-o {
  @apply rd-full bg-blue color-white text-base font-bold w-sm h-sm;

  $w: 26px;

  width: $w;
  height: $w;
}
</style>
