import { fetchApi, originalFetchApi, useFetchApi } from '~/composables/api'
import type { ErrorLevel } from '~/composables/enum'

export const downloadExcelTempPath = '/api/downloadExcelTemp'

// 参数接口
export interface GoodsPageParams {
  barCode?: string
  brandName?: string
  certificationId?: number
  endDate?: string
  goodsName?: string
  goodsState?: number
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: string
  pageIndex?: number
  pageSize?: number
  spec?: string
  startDate?: string
  userId: number
}

export interface GoodsPageResData {
  barCode: string
  brandName: string
  certificationId: number
  goodsId: number
  goodsName: string
  goodsState: number
  imageUrl: string
  isPrivary: boolean
  lastUpdatedDate: Record<string, unknown>
  netContent: string
  netContentUnit: string
  spec: string
  stateDate: Record<string, unknown>
  stateDesc: string
}

// 响应接口
export interface GoodsPageRes {
  data: GoodsPageResData[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 商品分页
 * @param {object} params qry
 * @param {string} params.barCode 条码信息
 * @param {string} params.brandName 品牌名称
 * @param {number} params.certificationId 认证id/企业id，因为前期不需要认证，所以不需要填写
 * @param {string} params.endDate 修改结束时间
 * @param {string} params.goodsName 产品名称
 * @param {number} params.goodsState 商品状态：-2：违规 ，-1：已下架，0：审核中，1：已上架，
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {string} params.spec 规格
 * @param {string} params.startDate 修改开始时间
 * @param {number} params.userId 用户id
 * @returns
 */
export const goodsPageApi = (params: GoodsPageParams, options?: any) =>
  useFetchApi<GoodsPageRes>('/api/goodsPage', {
    body: params,
    ...options,
  })

export const ImportGoodsPath = '/api/importGoods' // 批量导入商品

// 参数接口
export interface ChangeGoodsStateParams {
  goodsIds: number[]
  goodsState: number
  stateDesc?: string
}

// 响应接口
export interface ChangeGoodsStateRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 上下架商品
 * @param {object} params cmd
 * @param {number} params.goodsId 商品id
 * @param {number} params.goodsState 商品状态：-2：违规 ，-1：已下架，0：审核中，1：已上架，
 * @param {string} params.stateDesc 状态说明（违规、下架说明）
 * @returns
 */
export const changeGoodsStateApi = (params: ChangeGoodsStateParams, options?: any) =>
  fetchApi<ChangeGoodsStateRes>('/api/changeGoodsState', {
    method: 'POST',
    body: params,
    ...options,
  })

// 参数接口
export interface SelectGoodsCreateParams {
  certificationId?: number
  goodsIdList: number[]
  userId: number
}

// 响应接口
export interface SelectGoodsCreateRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 从通报产品导入商品
 * @param {object} params cmd
 * @param {number} params.certificationId 认证id
 * @param {array} params.goodsIdList 通报商品id集合
 * @param {number} params.userId 用户id
 * @returns
 */
export const selectGoodsCreateApi = (params: SelectGoodsCreateParams, options?: any) =>
  fetchApi<SelectGoodsCreateRes>('/api/selectGoodsCreate', {
    method: 'POST',
    body: params,
    ...options,
  })
/* export const selectGoodsCreateApi = (params: SelectGoodsCreateParams, options?: any) =>
  useFetchApi<SelectGoodsCreateRes>('/api/selectGoodsCreate', {
    body: params,
    ...options,
  }) */

// 参数接口
export interface GoodsCreateParams {
  attachment?: string
  barCode: string
  barType: string
  brandName: string
  certificationId?: number
  commonName: string
  companyPrice?: number
  currency?: string
  executeStandard?: string
  executeYear?: string
  expirationDay?: number
  extendAttribute?: string
  goodsContent?: string
  goodsDescription: string
  goodsName: string
  goodsType: string
  gpcType: string
  imageUrl?: string[]
  isPrivary: boolean
  keywords?: string
  marketDate?: string
  netContent: string
  netContentUnit: string
  onlineSaleWeb?: string
  origin?: string
  productFeatures?: string
  spec: string
  standardNumber?: string
  userId: number
  video?: string
}

// 响应接口
export interface GoodsCreateRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 新增商品
 * @param {object} params cmd
 * @param {string} params.attachment 附件（产品说明书、其他），json字符串。如：{"说明书":"http://XXXX","合格数":"http://"}
 * @param {string} params.barCode 条码
 * @param {string} params.barType 条码类型:UPC-A/EAN/ITF
 * @param {string} params.brandName 品牌名称
 * @param {number} params.certificationId 认证id
 * @param {string} params.commonName 产品通用名
 * @param {number} params.companyPrice 企业定价
 * @param {string} params.currency 币种
 * @param {string} params.executeStandard 执行标准,参考执行标准代码表，允许自行补充其他执行标准，如有多个以分号分隔。例：GB; GB/Z; NY
 * @param {string} params.executeYear 年份，如有多个以分号分隔。例：1998;2005;2022
 * @param {number} params.expirationDay 保质期/天
 * @param {string} params.extendAttribute 扩展属性:格式json字符串
 * @param {string} params.goodsContent 商品详细介绍
 * @param {string} params.goodsDescription 产品描述
 * @param {string} params.goodsName 产品名称
 * @param {string} params.goodsType 产品状态填写“在产或不在产”
 * @param {string} params.gpcType 产品（GPC）分类
 * @param {array} params.imageUrl 主图，轮播的第一张图
 * @param {boolean} params.isPrivary 是否保密
 * @param {string} params.keywords 关键词，多个用”|“隔开，冗余
 * @param {string} params.marketDate （预计）上市时间
 * @param {string} params.netContent 净含量
 * @param {string} params.netContentUnit 净含量单位
 * @param {string} params.onlineSaleWeb 线上销售网站
 * @param {string} params.origin 原产地
 * @param {string} params.productFeatures 产品特征
 * @param {string} params.spec 规格
 * @param {string} params.standardNumber 标准号,如有多个以分号分隔。例：23225; 45235; 46346
 * @param {number} params.userId 用户id
 * @param {string} params.video 视频
 * @returns
 */
export const goodsCreateApi = (params: GoodsCreateParams, options?: any) =>
  fetchApi<GoodsCreateRes>('/api/goodsCreate', {
    method: 'POST',
    body: params,
    ...options,
  })

// Nuxt server 接口
export const getGPCidPath = '/api/GetGPC'

// 参数接口
export interface GoodsDelParams {
  goodsId: number
}

// 响应接口
export interface GoodsDelRes {
  errCode: string
  errMessage: string
  success: boolean
}
export const goodsDelApi = (params: GoodsDelParams, options?: any) =>
  fetchApi<GoodsDelRes>('/api/goodsDel', {
    method: 'POST',
    body: params,
    ...options,
  })

export interface GoodsByteQrCodeImageParams {
  errorLevel: ErrorLevel // 7%,15%,25%,30%
  goodsId: number
  width: number
  height: number
}

/**
 * 返回二进制二维码图片，参数是商品id,宽,高,纠错等级（7%,15%,25%,30%）
 * @param {string} errorLevel errorLevel
 * @param {string} goodsId goodsId
 * @param {string} height height
 * @param {string} width width
 * @returns
 */
export const goodsByteQrCodeImageApi = (params: GoodsByteQrCodeImageParams) => {
  const paramsStr = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
  return originalFetchApi(`/api/goodsByteQrCodeImage?${paramsStr}`)
}

// 响应接口
export interface GetStsTokenRes {
  data: {
    accessKeyId: string
    accessKeySecret: string
    expiration: string
    securityToken: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
export const getStsTokenForOssApi = (options?: any) =>
  fetchApi<GetStsTokenRes>('/sts/getStsTokenForOss', {
    method: 'POST',
    ...options,
  })

// 参数接口
export interface GoodsLoadParams {
  goodsId: number
}

export interface GoodsLoadResData {
  attachment: string
  barCode: string
  barType: string
  brandName: string
  certificationId: number
  commonName: string
  companyPrice: number
  createdDate: Record<string, unknown>
  currency: string
  executeStandard: string
  executeYear: string
  expirationDay: number
  extendAttribute: string
  goodsCode: string
  goodsContent: string
  goodsDescription: string
  goodsId: number
  goodsName: string
  goodsState: number
  goodsType: string
  gpcType: string
  gpcTypeStr: string
  imageUrl: string
  isPrivary: boolean
  keywords: string
  lastUpdatedDate: Record<string, unknown>
  marketDate: string
  netContent: string
  netContentUnit: string
  onlineSaleWeb: string
  origin: string
  productFeatures: string
  sliderImages: string
  spec: string
  standardNumber: string
  stateDate: Record<string, unknown>
  stateDesc: string
  video: string
}

// 响应接口
export interface GoodsLoadRes {
  data: GoodsLoadResData
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 加载商品
 * @param {object} params qry
 * @param {number} params.goodsId 商品id
 * @returns
 */
export const goodsLoadApi = (params: GoodsLoadParams, options?: any) =>
  fetchApi<GoodsLoadRes>('/api/goodsLoad', {
    method: 'POST',
    body: params,
    ...options,
  })

// 参数接口
export interface GoodsUpdateParams {
  attachment?: string
  barCode: string
  barType: string
  brandName: string
  commonName: string
  companyPrice?: number
  currency?: string
  executeStandard?: string
  executeYear?: string
  expirationDay?: number
  extendAttribute?: string
  goodsContent?: string
  goodsDescription: string
  goodsId: number
  goodsName: string
  goodsType: string
  gpcType: string
  imageUrl?: string[]
  isPrivary: boolean
  keywords?: string
  marketDate?: string
  netContent: string
  netContentUnit: string
  onlineSaleWeb?: string
  origin?: string
  productFeatures?: string
  spec: string
  standardNumber?: string
  userId: number
  video?: string
}
// 响应接口
export interface GoodsUpdateRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 编辑商品
 * @param {object} params cmd
 * @param {string} params.attachment 附件（产品说明书、其他）
 * @param {string} params.barCode 条码
 * @param {string} params.barType 条码类型:UPC-A/EAN/ITF
 * @param {string} params.brandName 品牌名称
 * @param {string} params.commonName 产品通用名
 * @param {number} params.companyPrice 企业定价
 * @param {string} params.currency 币种
 * @param {string} params.executeStandard 执行标准,参考执行标准代码表，允许自行补充其他执行标准，如有多个以分号分隔。例：GB; GB/Z; NY
 * @param {string} params.executeYear 年份，如有多个以分号分隔。例：1998;2005;2022
 * @param {number} params.expirationDay 保质期/天
 * @param {string} params.extendAttribute 扩展属性:格式json字符串
 * @param {string} params.goodsContent 商品详细介绍
 * @param {string} params.goodsDescription 产品描述
 * @param {number} params.goodsId 商品id
 * @param {string} params.goodsName 产品名称
 * @param {string} params.goodsType 产品状态填写“在产或不在产”
 * @param {string} params.gpcType 产品（GPC）分类
 * @param {array} params.imageUrl 主图，轮播的第一张图
 * @param {boolean} params.isPrivary 是否保密
 * @param {string} params.keywords 关键词，多个用”|“隔开，冗余
 * @param {string} params.marketDate （预计）上市时间
 * @param {string} params.netContent 净含量
 * @param {string} params.netContentUnit 净含量单位
 * @param {string} params.onlineSaleWeb 线上销售网站
 * @param {string} params.origin 原产地
 * @param {string} params.productFeatures 产品特征
 * @param {string} params.spec 规格
 * @param {string} params.standardNumber 标准号,如有多个以分号分隔。例：23225; 45235; 46346
 * @param {number} params.userId 用户id
 * @param {string} params.video 视频
 * @returns
 */
export const goodsUpdateApi = (params: GoodsUpdateParams, options?: any) =>
  fetchApi<GoodsUpdateRes>('/api/goodsUpdate', {
    method: 'POST',
    body: params,
    ...options,
  })
