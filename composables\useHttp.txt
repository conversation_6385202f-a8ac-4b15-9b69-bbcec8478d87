// /composables/useHttp.ts

import type { FetchError, FetchResponse, SearchParameters } from 'ofetch'
import { hash } from 'ohash'
import type { AsyncData, UseFetchOptions } from '#app'
import type { KeysOf, PickFrom } from '#app/composables/asyncData'
import { userStore } from '~/stores/userStore'

type UrlType = string | Request | Ref<string | Request> | (() => string | Request)

export type HttpOption<T> = UseFetchOptions<ResOptions<T>, T, KeysOf<T>, any>

interface ResOptions<T> {
  data: T
  code: number
  success: boolean
  detail?: string
}

const handleError = <T>(
  _method: string | undefined,
  _response: FetchResponse<ResOptions<T>> & FetchResponse<ResponseType>,
) => {
  // Handle the error
  doOnResponseError(_response)
}

const checkRef = (obj: Record<string, any>) =>
  Object.keys(obj).some(key => isRef(obj[key]))

const truncateString = (str: string) => {
  if (str?.length > 40) {
    return str.slice(0, 40) + '...' // 截断并添加省略号
  }
  return str // 返回原字符串
}

const doOnResponse = (response: FetchResponse<any>) => {
  const refreshToken = response.headers.get('refreshtoken')
  if (refreshToken !== null && refreshToken !== '') {
    userStore().setToken(refreshToken)
  }
  // 当response.status为401时，onResponse和onResponseError都会执行
  if (response.status >= 200 && response.status < 300) {
    const d = response._data
    if (!d.success) {
      if (d?.errMessage === 'Token过期') {
        userStore().logout()
        navigateTo({
          path: '/login',
          query: {
            msgType: 'error',
            msg: '登录过期，请重新登录',
          },
        })
      }
      else {
        // response._data里面是后天返回的数据，这里code跟上面的code不是同一个
        // _data.code 是后台跟前端约定的状态码，或者是约定_data.success 判断

        // 这里如果要给用户提示错误，要分服务端还是客户端
        // console.log(response._data.errMessage)
        if (import.meta.client) {
          ElMessage.error('错误：' + truncateString(response._data.errMessage))
        }
        else {
          if (import.meta.client) {
            // 跳转到错误提示页
            // const nuxtApp = useNuxtApp()
            useNuxtApp().runWithContext(() => {
              navigateTo({
                path: '/myError',
                query: {
                  msgType: 'error',
                  msg: truncateString(response._data.errMessage),
                },
              })
            })
          }
        }
      }
    }
  }
}

const doOnResponseError = (response: FetchResponse<any>) => {
  const nuxtApp = useNuxtApp()
  if (import.meta.client) {
    // ElMessage.error('客户端错误')
    ElMessage.error(truncateString(response._data.errMessage))
  }
  else {
    console.log('onResponseError')
    // 跳转到错误提示页
    nuxtApp.runWithContext(() => {
      navigateTo({
        path: '/myError',
        query: {
          msgType: 'error',
          msg: truncateString(response._data.errMessage),
        },
      })
    })
  }
}

const myFetch = <T>(url: UrlType, opts: HttpOption<T>) => {
  // Check the `key` option
  const { key, params, watch } = opts
  if (!key && ((params && checkRef(params)) || (watch && checkRef(watch))))
    console.error('\x1B[31m%s\x1B[0m %s', '[useHttp] [error]', 'The `key` option is required when `params` or `watch` has ref properties, please set a unique key for the current request.')

  const options = opts as UseFetchOptions<ResOptions<T>>
  // 不阻塞导航: 当 lazy 设置为 true，页面在请求数据期间不会被阻塞。
  // 这意味着用户可以立即看到页面内容，而不是等待数据加载完成后再显示页面。
  // 这对于提升用户体验非常重要，因为它减少了页面加载时的等待时间。
  // 初始状态处理: 在使用 lazy: true 时，返回的数据会在请求完成之前保持为 null。
  // 这要求开发者在处理数据时考虑到这种情况，并可能需要设置默认值，以避免在页面渲染时出现错误或空白。
  options.lazy = options.lazy ?? true

  // const { apiBaseUrl } = useRuntimeConfig().public

  return useFetch<ResOptions<T>>(url, {
    // Request interception
    onRequest({ options }) {
      // Set the base URL
      options.baseURL = useRuntimeConfig().public.apiBaseUrl
      // Set the request headers
      /*            const { $i18n } = useNuxtApp()
                  const locale = $i18n.locale.value
                  options.headers = new Headers(options.headers)
                  options.headers.set('Content-Language', locale) */
      options.headers.set('Authorization', userStore().getToken())
    },
    // Response interception
    onResponse(_context) {
      // Handle the response
      const { response } = _context
      doOnResponse(response)
    },
    // Error interception
    onResponseError({ response, options: { method } }) {
      handleError<T>(method, response)
    },
    // Set the cache key
    key: key ?? hash(['api-myFetch', url, JSON.stringify(options)]),
    // Merge the options
    ...options,
  }) as AsyncData<PickFrom<T, KeysOf<T>>, FetchError<ResOptions<T>> | null>
}

export const useHttp = {
  get: <T>(url: UrlType, params?: SearchParameters, option?: HttpOption<T>) => {
    return myFetch<T>(url, { method: 'get', params, ...option })
  },

  post: <T>(url: UrlType, body?: RequestInit['body'] | Record<string, any>, option?: HttpOption<T>) => {
    return myFetch<T>(url, { method: 'post', body, ...option })
  },

  put: <T>(url: UrlType, body?: RequestInit['body'] | Record<string, any>, option?: HttpOption<T>) => {
    return myFetch<T>(url, { method: 'put', body, ...option })
  },

  delete: <T>(url: UrlType, body?: RequestInit['body'] | Record<string, any>, option?: HttpOption<T>) => {
    return myFetch<T>(url, { method: 'delete', body, ...option })
  },
}
