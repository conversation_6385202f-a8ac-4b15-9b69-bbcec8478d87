import { v4 } from 'uuid'

export const addImgUrlType = (img: string) => {
  return 'url(' + img + ')'
}

/**
 * 用户名简单快速加密，用于文件命名，解密时能找到用户所有图片
 * @param str
 */
export const userCodeEncrypt = (str: string) => {
  let encrypted = ''
  for (const char of str) {
    // 将字符转换为ASCII值并加上一个常数（例如100）
    const ascii = char.charCodeAt(0) + 100
    encrypted += ascii.toString()
  }
  return encrypted
}

export const getRandomFileName = (path: string, fileName: string) => {
  // 提取后缀名
  const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1)
  const code = userCodeEncrypt(userStore().userCode)
  const fullUUID = v4()
  return '/user/' + code + path + fullUUID + '.' + fileExtension
}

export function replaceDomain(url: string): string {
  const newBase = 'oss.gs1helper.com'
  // 分割URL为数组
  const parts = url.split('/')
  // 检查是否有足够的部分来替换
  if (parts.length < 4) {
    // throw new Error('URL is not long enough to have a third slash.')
    return url
  }
  // 保留协议部分（如果有）
  const protocol = parts[0] + '//'
  // 将新前缀与剩余部分重新组合
  const restOfUrl = parts.slice(3).join('/')
  // 返回新的URL
  return `${protocol}${newBase}/${restOfUrl}`
}

/**
 * 获取文件大小
 * @param size 单位为字节
 */
export function getFileSizeStr(size: number) {
  if (size < 1024) {
    return size + 'B'
  }
  else if (size < 1024 * 1024) {
    return Math.round(size / 1024) + 'KB'
  }
  else {
    return Math.round(size / 1024 / 1024) + 'MB'
  }
}
