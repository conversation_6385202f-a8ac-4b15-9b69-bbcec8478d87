import { getUserServerInfoApi } from '~/composables/apiUser'

export default defineNuxtRouteMiddleware(async (to, from) => {
  const mustHaveTokenUrl = [
    '/infoReport',
    '/infoReport/reportGoodsList',
    '/miniShop',
    '/miniShop/create',
  ]

  if (mustHaveTokenUrl.includes(to.path) && !userStore().getToken()) {
    return navigateTo({
      path: '/login',
      query: {
        msgType: 'error',
        msg: '请先登录',
      },
    }, {
      // 使用外部方式跳转，避免客户端跳转时NuxtPage的slot冲突
      external: true,
      redirectCode: 301,
    })
  }
  else {
    const nuxtApp = useNuxtApp()
    if (nuxtApp.isHydrating) return // 避免重复调用

    switch (to.path) {
      case '/miniShop':
      case '/miniShop/create':
        try {
          const data = await getUserServerInfoApi({
            userId: Number(userStore().userId),
          })
          if (!data.data.qrAuth) {
            console.log('res.data.qrAuth', data.data.qrAuth)
            return navigateTo({
              path: '/miniShop/introduce',
              query: {
                msgType: 'warning',
                msg: '请先开通二维码微站',
              },
            }, {
              external: true,
              redirectCode: 301,
            })
          }
        }
        catch (e) {
          console.log(e)
          return navigateTo({
            path: '/miniShop/introduce',
            query: {
              msgType: 'warning',
              msg: '请先开通二维码微站',
            },
          }, {
            external: true,
            redirectCode: 301,
          })
        }
    }
  }
})
