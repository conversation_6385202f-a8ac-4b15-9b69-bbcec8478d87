<script setup lang="ts">
const { id } = useRoute().params

const { data, status, error, refresh, clear } = await useArticleLoadData({
  articleId: Number(id),
}, d => (
  d.data
))

const context = computed(() => {
  return updateVideoDimensions(data.value.articleConent)
})

const updateVideoDimensions = (articleContent: string) => {
  // 正则表达式用于查找 <video> 标签，并捕获 width 和 height 属性
  const videoRegex = /<video[^>]*width="[^"]*"[^>]*height="[^"]*"[^>]*>/gi

  // 替换函数，它会将匹配到的 <video> 标签中的 width 和 height 更新为目标值
  const replaceFn = (match: string) => {
    return match
      .replace(/width="[^"]*"/, 'width="100%"')
      .replace(/height="[^"]*"/, 'height="400px"')
  }

  // 使用正则表达式和替换函数来更新 articleContent 中的所有 <video> 标签
  return articleContent.replace(videoRegex, replaceFn)
}
</script>

<template>
  <div class="max-w-640px px-10 mx-auto">
    <div class="p-10 mt-6 text-center text-2xl font-bold">
      {{ data.articleTitle }}
    </div>
    <el-divider />
    <div
      class="f-html font-size-16px line-height-relaxed pb-10"
      v-html="context"
    />
    <div class="w-md m-auto">
      <el-divider>
        ·
      </el-divider>
      <NuxtImg
        class="m-auto block pt-6 pb-20"
        width="120"
        src="/resource/images/logo_big.png"
        alt="logo"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.f-html {
  ::v-deep(img) {
    width: 100%;
  }

  ::v-deep(.ace-line) {
    @apply py-4;
  }
}
</style>
