export enum OrderDirection {
  def = '',
  asc = 'asc', // 升序
  desc = 'desc', // 降序
}

// 支付状态，-1:新建；0：未支付，1：已支付（支付成功），2：支付失败，
export enum PayState {
  new = -1,
  notPay = 0,
  paid = 1,
  failed = 2,
}

// OSS 文件路径
export enum OssFilePath {
  goodsImg = '/mini-shop/image/goods-img/', // 产品主图
  descriptionImg = '/mini-shop/image/description-img/', // 详情页
  goodsVideo = '/mini-shop/video/goods-video/', // 产品相关视频
  attachment = '/mini-shop/attachment/', // 附件
}

export enum GoodState {
  violation = -2,
  down = -1,
  review = 0,
  up = 1,
}

export enum ErrorLevel {
  low = '7%',
  normal = '15%',
  medium = '25%',
  high = '30%',
}

export enum ServerType {
  makeFilm = 1, // 胶片制作
  infoReport = 2, // 产品信息上报
  design = 3, // 设计
  labelPrint = 4, // 标签
  storeCode = 5, // 店内码
  miniShop = 11, // 二维码微站
}

/**
 * 来源平台
 * @param wx 微信:商用条码生成器
 * @param douYin 抖音
 * @param other 其他
 * @param wx_tiaoMaBang 微信:条码帮
 */
export enum FromPlatform {
  wx = 1,
  douYin = 2,
  other = 3,
  wx_tiaoMaBang = 4,
}