<script setup lang="ts">
import { initializeKinesis } from '@amineyarman/kinesis'
import type { TabsPaneContext } from 'element-plus'
import { DesignImage } from '~/config/image'

const props = defineProps<{
  articleTypeId: number
  data: any
  articleList: any
}>()

onMounted(() => {
  if (import.meta.client) {
    initializeKinesis()
  }
})

const id = ref(props.articleTypeId)
const emit = defineEmits([
  'update-id',
])

watch(id, () => {
  emit('update-id', id.value)
})

const getHref = (d: any) => {
  if (d.articleUrl) {
    return d.articleUrl
  }
  else {
    return `/tutorials/${d.articleId}`
  }
}

const handleTabsClick = (tab: TabsPaneContext, event: Event) => {

}
</script>

<template>
  <div
    class="text-center text-3xl py-8 mt-4"
  >
    条码规范 · 知识学习
  </div>
  <el-tabs
    v-model="id"
    class="o-flex-c"
    @tab-click="handleTabsClick"
  >
    <el-tab-pane
      v-for="tab in data[0]?.itemList"
      :key="tab.articleTypeId"
      :label="tab.typeName"
      :name="tab.articleTypeId"
    />
  </el-tabs>
  <transition-group
    name="fade"
    class="f-article-box w-full mt-10 grid justify-items-center gap-8 op-100"
    tag="div"
  >
    <a
      v-for="item in articleList"
      :key="item.articleId"
      class="f-article-item cursor-pointer o-transition-fast hover:color-blue"
      :href="getHref(item)"
      target="_blank"
    >
      <div
        class="f-article-img overflow-hidden rd-2 op-90 hover:op-100"
        data-kinesisdistance-item
        data-ks-strength="15"
        data-ks-transform="scale"
        data-ks-interaction="attraction"
        data-ks-startdistance="120"
        data-ks-duration="500"
      >
        <NuxtImg
          :src="item.imageUrl ? item.imageUrl: DesignImage.tutorials.defaultMainImg"
          alt=""
        />
      </div>
      <div class="o-inline-2 mt-4">
        {{ item.articleTitle }}
      </div>
    </a>
  </transition-group>
</template>

<style scoped lang="scss">
.f-article-box {
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
}

.f-article-item {
  width: 260px;
  height: 230px;

  .f-article-img {
    width: 100%;
    height: 180px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

.fade-enter-active {
  transition: all 2s ease;
}
</style>
