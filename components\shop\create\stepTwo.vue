<script setup lang='ts'>
const useStepStore = miniShopCreateStepStore()
const {
  createTypeStep2,
} = storeToRefs(useStepStore)
</script>

<template>
  <div>
    <ShopCreateFromReport v-if="createTypeStep2 === 'report'" />
    <ShopCreateFromUpload v-if="createTypeStep2 === 'upload'" />
    <ShopCreateFromCreate v-if="createTypeStep2 === 'create'" />
  </div>
</template>

<style scoped lang='scss'>

</style>
