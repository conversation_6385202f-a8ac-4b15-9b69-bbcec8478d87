<script setup lang="ts">
import Editor from '@tinymce/tinymce-vue'

import { TINYMCE_KEY } from '~/composables/constant'

const contentHTML = ref('<h1>This is html header</h1>')
</script>

<template>
  <ClientOnly fallback-tag="div" />
  <Editor
    :api-key="TINYMCE_KEY"
    :init="{
      plugins: 'lists link image table code help wordcount',
      language: 'zh_CN',
      language_url: '/other/Tinymce_zh_CN.js',
    }"
  />
</template>

<style scoped lang="scss">

</style>
