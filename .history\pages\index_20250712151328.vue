<script lang="ts" setup>
import isMobile from 'is-mobile'
import { useArticleListData } from '~/composables/apiArticle'

const router = useRouter()

const articleTypeId = ref(0)
const showMobileTip = ref(false)

onMounted(() => {
  if (import.meta.client && isMobile()) {
    showMobileTip.value = true
  }
})

const { data, status, error, refresh, clear } = await useArticleListData({
  articleTypeId: 1,
})

watch(data, () => {
  if (data.value?.length > 0)
    articleTypeId.value = data.value[0].itemList[0].articleTypeId
}, { immediate: true })

const articleList = computed(() => {
  const foundItem = data.value[0].itemList.find(item => item.articleTypeId === articleTypeId.value)
  return foundItem ? foundItem.articleList.slice(0, 10) : []
})

const updateArticleTypeId = (id: number) => {
  articleTypeId.value = id
}
</script>

<template>
  <div class="w-full">
    <CarouselPicFilm />
    <div class="max-w-1536px pt-6 pb-10 px-20 mx-auto">
      <div class="flex gap-8">
        <TileThreeIcon
          icon="i-ri:barcode-line"
          title="商品条码"
          @click="navigateTo('/makeFilm')"
        />
        <TileThreeIcon
          icon="i-ri:qr-code-line"
          title="二维码生成"
          @click="navigateTo('/')"
        />
        <TileThreeIcon
          icon="i-ri:upload-cloud-2-line"
          title="信息通报"
          @click="navigateTo('/infoReport')"
        />
      </div>
      <TutorialTab
        v-if="status === 'success' && data && articleList"
        :article-list="articleList"
        :article-type-id="articleTypeId"
        :data="data"
        @update-id="updateArticleTypeId"
      />
      <div
        v-else-if="status === 'error'"
        class="text-center py-8 text-red-500"
      >
        数据加载失败，请刷新页面重试
      </div>
      <div
        v-else-if="status === 'pending'"
        class="text-center py-8 text-gray-500"
      >
        正在加载...
      </div>
      <div class="w-full o-flex-c p-10 mt-10">
        <el-button
          @click="navigateTo('/tutorials')"
        >
          查看更多
        </el-button>
      </div>
    </div>
    <el-dialog
      v-model="showMobileTip"
      class="f-dialog"
    >
      <div class="flex flex-col items-center px-2">
        <div class="text-center">
          请在PC端访问本网站<br>手机端前前往小程序
        </div>
        <NuxtImg
          alt="QR code"
          class="mt-4 block"
          src="/resource/images/makeFilm/appCode_from_websiteWxQr.png"
        />
        <a
          class="block w-fit mt-4 px-9 py-4 rd-2 bg-blue color-white"
          href="https://wx.gs1helper.com/12"
          target="_blank"
        >扫码前往微信小程序</a>
      </div>
      <!--      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            Cancel
          </el-button>
          <el-button
            type="primary"
            @click="dialogVisible = false"
          >
            Confirm
          </el-button>
        </div>
      </template> -->
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.f-dialog) {
  width: min(300px, 80vw);
}
</style>
