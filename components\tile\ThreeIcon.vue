<script lang="ts" setup>
defineProps<{
  title: string
  icon: string
}>()
</script>

<template>
  <div
    class="f-business-box cursor-pointer o-bg-blue-light o-flex-c o-color-default-black hover:color-blue o-transition-fast"
  >
    <div class="f-ico-blur-box absolute o-transition-fast">
      <div
        :class="icon"
        class="f-ico-blur color-white z-1"
      />
    </div>
    <div class="f-ico-right-box o-transition-fast">
      <div
        :class="icon"
        class="f-ico-right absolute color-white z-2"
      />
    </div>
    <div
      :class="icon"
      class="f-ico-center z-3 o-transition-fast"
    />
    <div class="text-2xl z-5">
      {{ title }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.f-business-box {
  $icon-size: 264px;

  @apply overflow-hidden grow-1 rd-2 relative flex-col;

  min-width: 200px;
  height: 155px;

  &:hover {
    .f-ico-blur-box {
      opacity: 50%;
      filter: blur(0px);
    }

    .f-ico-right-box {
      opacity: 90%;
      filter: blur(8px);
    }

    .f-ico-center {
      transform: scale(1.2);
    }
  }

  .f-ico-blur-box {
    top: - calc($icon-size / 2);
    left: -10%;
    opacity: 90%;
    filter: blur(8px);
  }

  .f-ico-blur {
    font-size: $icon-size;
  }

  .f-ico-right-box {
    opacity: 50%;
    bottom: - calc($icon-size / 2);
    right: -10%;
  }

  .f-ico-right {
    font-size: $icon-size;
  }

  .f-ico-center {
    font-size: 60px;
  }
}
</style>
