<script setup lang='ts'>
import { <PERSON><PERSON><PERSON><PERSON>, ArrowR<PERSON>, ArrowRightBold } from '@element-plus/icons-vue'
import { usePagination } from '~/composables/usePagination'
import type { ReportGoodsPageResData } from '~/composables/apiReport'
import { selectGoodsCreateApi } from '~/composables/apiMiniShop'

const useStepStore = miniShopCreateStepStore()
const { activeStep } = storeToRefs(useStepStore)
const {
  pageSize,
  pageIndex,
  totalCount,
  tableHeight,
} = usePagination(500, 300)

const listLeft = ref<ReportGoodsPageResData[]>([])
const listRight = ref<ReportGoodsPageResData[]>([])

const formInline = reactive({
  barCode: '',
  goodsName: '',
})
const formBarCode = ref('')
const formGoodsName = ref('')
const { data, status, error, refresh, clear } = await reportGoodsPageApi({
  barCode: formBarCode,
  goodsName: formGoodsName,
  needTotalCount: true,
  orderBy: '',
  orderDirection: OrderDirection.def,
  pageIndex: pageIndex,
  pageSize: 100,
  userId: Number(userStore().userId),
})
watch(data, () => {
  if (data.value?.data) {
    listLeft.value = data.value.data
    totalCount.value = data.value.totalPages
  }
}, { immediate: true })

const loading = computed(() => status.value === 'pending')

const handleSearch = () => {
  formBarCode.value = formInline.barCode
  formGoodsName.value = formInline.goodsName
}

const multipleSelectionLeft = ref<never[]>([])
const multipleSelectionRight = ref<never[]>([])

const handleSelectionLeft = (val: never[]) => {
  multipleSelectionLeft.value = val
}
const handleSelectionRight = (val: never[]) => {
  multipleSelectionRight.value = val
}
const handleToRight = () => {
  if (multipleSelectionLeft.value.length > 0) {
    multipleSelectionLeft.value.forEach((item: ReportGoodsPageResData) => {
      // 检查 listRight 中是否有相同 goodsId 的元素
      const exists = listRight.value.some(rightItem => rightItem.goodsId === item.goodsId)
      // 如果不存在相同的 goodsId，则添加到 listRight 中
      if (!exists) {
        listRight.value.push(item)
      }
    })
  }
}
const handleToLeft = () => {
  if (multipleSelectionRight.value.length > 0) {
    listRight.value = listRight.value.filter(item =>
      !multipleSelectionRight.value.some((leftItem: ReportGoodsPageResData) => leftItem.goodsId === item.goodsId),
    )
  }
}
const sa = ref()
const handleSubmit = async () => {
  const arr = listRight.value.map(item => item.goodsId)
  /*  selectGoodsCreateApi({
    goodsIdList: arr, userId: Number(userStore().userId),
  }).then((res) => {
    const { data, status, error, refresh, clear } = res
    if (!error && data.success) {
      activeStep.value = 3
    }
  }) */
  /*  const { data, status, error, refresh, clear } = await selectGoodsCreateApi({
    goodsIdList: arr, userId: Number(userStore().userId),
  })
  sa.value = status.value
  if (status.value === 'success') {
    activeStep.value = 3
  } */
  /*  fetchApi('/api/selectGoodsCreate', {
    method: 'POST',
    body: {
      goodsIdList: arr, userId: Number(userStore().userId),
    },
  }).then((res) => {
    if (res.success) {
      activeStep.value = 3
    }
  }) */
  selectGoodsCreateApi({
    goodsIdList: arr, userId: Number(userStore().userId),
  }).then((res) => {
    if (res.success) {
      activeStep.value = 3
    }
  })
}
// TODO 穿梭时左侧要体现右侧已有的
</script>

<template>
  {{ sa }}
  <div class="color-gray mb-2">
    <strong class="o-color-default-black">步骤说明：</strong>1. 勾选左侧通报过的产品。2. 点中间<span class="text-sm bg-blue color-white pt-1 px-1 rd-1 mx-1"><el-icon><ArrowRightBold /></el-icon></span>按钮让产品附加到右侧。3. 按下一步。
  </div>
  <div class="flex items-center gap-4">
    <div class="f-left o-border">
      <div class="flex gap-2">
        <el-input
          v-model="formInline.barCode"
          placeholder="商品条码"
          style="width: 160px"
          clearable
        />
        <el-input
          v-model="formInline.goodsName"
          placeholder="产品名称"
          style="width: 160px"
          clearable
        />
        <el-button
          @click="handleSearch"
        >
          <div class="i-ri:search-line" />
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="listLeft"
        :height="tableHeight"
        row-key="goodsId"
        style="width: 100%"
        @selection-change="handleSelectionLeft"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        <el-table-column
          prop="barCode"
          label="商品条码"
          width="160"
        />
        <el-table-column
          prop="goodsName"
          label="产品名称"
        >
          <template #default="scope">
            <div class="o-inline-2">
              {{ scope.row.goodsName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="spec"
          label="规格"
          width="100"
        />
      </el-table>
      <el-pagination
        v-model:current-page="pageIndex"
        :page-size="100"
        :pager-count="5"
        :total="totalCount"
        class="float-right mt-4"
        layout="prev, pager, next, jumper"
      />
    </div>
    <div class="f-center flex-col o-flex-c">
      <div>
        <el-button
          :icon="ArrowRight"
          type="primary"
          :disabled="multipleSelectionLeft.length<=0"
          @click="handleToRight"
        />
      </div>
      <div class="mt-4">
        <el-button
          :icon="ArrowLeft"
          type="primary"
          :disabled="multipleSelectionRight.length<=0"
          @click="handleToLeft"
        />
      </div>
    </div>
    <div class="f-right o-border">
      <!--      <div class="flex gap-2">
        <el-input
          v-model="formInline.barCode"
          placeholder="商品条码"
          style="width: 160px"
          row-key="goodsId"
          clearable
        />
        <el-input
          v-model="formInline.goodsName"
          placeholder="产品名称"
          style="width: 160px"
          clearable
        />
        <el-button
          @click="handleSearch"
        >
          <div class="i-ri:search-line" />
        </el-button>
      </div> -->
      <el-table
        v-loading="loading"
        :data="listRight"
        :height="tableHeight + 78"
        style="width: 100%"
        @selection-change="handleSelectionRight"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        <el-table-column
          prop="barCode"
          label="商品条码"
          width="160"
        />
        <el-table-column
          prop="goodsName"
          label="产品名称"
        >
          <template #default="scope">
            <div class="o-inline-2">
              {{ scope.row.goodsName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="spec"
          label="规格"
          width="100"
        />
      </el-table>
    </div>
  </div>
  <el-button
    class="mt-8 mr-20"
    @click="useStepStore.stepBack"
  >
    上一步
  </el-button>
  <el-button
    class="mt-8"
    type="primary"
    :disabled="listRight.length <= 0"
    @click="handleSubmit"
  >
    下一步
  </el-button>
</template>

<style scoped lang='scss'>
$w:50px;
.f-center{
  width: $w;
}

.f-left,
.f-right{
  width: calc((100% - $w)/2);
  min-width: 400px;
}
</style>
