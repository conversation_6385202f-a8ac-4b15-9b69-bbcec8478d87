<script lang='ts' setup>
import type { FormInstance, FormRules, UploadUserFile } from 'element-plus'
import { useWindowSize } from '@vueuse/core'
import { reportFileRecordDelApi, reportFileRecordPageApi } from '~/composables/apiReport'
import { getUserServerInfoApi, updateUserPhone<PERSON>pi } from '~/composables/apiUser'

const { height } = useWindowSize()

const tableHeight = computed(() => {
  const reduce = 550
  const min = 260
  if (import.meta.client) {
    return Math.max(height.value - reduce, min) || min
  }
  else {
    return min
  }
})

// 是否已保存手机号，用于要手机号才能上传
// const havePhone = ref(false)
const serveSelect = ref('1')
const fileList = ref<UploadUserFile[]>([])
const step = ref(0)

const { data, status, error, refresh, clear } = await reportFileRecordPageApi({
  needTotalCount: true,
  orderBy: 'uploadDate',
  orderDirection: OrderDirection.desc,
  pageIndex: 1,
  pageSize: 100000,
  userId: Number(userStore().userId),
})

// 每次都拿是对的，不能购买了刷新不出来，要退出登录的时候才调用就不对了。
/* const { data: userServerData, refresh: refreshUserServerData }
  = await getUserServerInfoApi({
    userId: Number(userStore().userId),
  }) */

const { data: userServerData, refresh: refreshUserServerData } = await useAsyncData(
  'getUserServerInfoApi',
  () => new Promise((resolve, reject) => getUserServerInfoApi({
    userId: Number(userStore().userId),
  }).then((res) => {
    resolve(res.data)
  }).catch((err) => {
    reject(err)
  }),
  ),
)

interface RuleForm {
  phone: string
  barCodeCardNum: string
  barCodeCardPassword: string
}
const ruleForm = reactive({
  phone: '',
  barCodeCardNum: '',
  barCodeCardPassword: '',
})
const showStep = ref(true)
const reportAuth = ref(false)
const authDialogVisible = ref(false)
const dialogVisible = ref(false)
const orderContent = ref('')
const ruleFormRef = ref<FormInstance>()

watch(userServerData, () => {
  if (userServerData.value) {
    ruleForm.phone = userServerData.value.phone
    ruleForm.barCodeCardNum = userServerData.value.barCodeCardNum
    ruleForm.barCodeCardPassword = userServerData.value.barCodeCardPassword
    reportAuth.value = userServerData.value.reportAuth
    orderContent.value = userServerData.value.reportOrderInfo?.orderContent
  }
}, { immediate: true })

watch(dialogVisible, () => {
  refreshUserServerData()
})

const handleDownLoadTemp = () => {
  useDownloadFile(downloadExcelTempPath)
}

const handleDel = (id: number) => {
  reportFileRecordDelApi({
    fileRecordId: id,
  }).then(() => {
    ElMessage.success('删除成功')
    refresh()
  })
}
const handleDownLoad = (id: number) => {
  useDownloadFile(downloadReportPath + '?fileRecordId=' + id)
}

const uploadSuccess = () => {
  dialogVisible.value = false
  if (reportAuth.value) {
    ElMessage.success('上传成功，请留意反馈短信或电话')
  }
  else {
    authDialogVisible.value = true
  }
  refresh()
}

const uploadFailed = () => {
  ElMessage.error('上传失败')
}

const rules = reactive<FormRules<RuleForm>>({
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^1[3-8|9]\d{9}$/,
      message: '请输入正确手机号',
      trigger: 'blur',
    },
  ],
  barCodeCardNum: [{ required: true, message: '请输入条码卡号', trigger: 'blur' }],
  barCodeCardPassword: [{ required: true, message: '请输入密码', trigger: 'blur' }],
})

const handleShowDialog = () => {
  step.value = 0
  dialogVisible.value = true
}

const submitForm = async () => {
  if (step.value !== 0) return
  if (!ruleFormRef.value) return
  await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      updateUserPhoneApi({
        barCodeCardNum: ruleForm.barCodeCardNum,
        barCodeCardPassword: ruleForm.barCodeCardPassword,
        phone: ruleForm.phone,
        userId: Number(userStore().userId),
      }).then(() => {
        step.value = 1
      })
    }
  })
}

const handleChanel = () => {
  if (step.value === 0) {
    dialogVisible.value = false
  }
  else {
    step.value = 0
  }
}
</script>

<template>
  <div class="w-full o-bg-blue-light">
    <AppMenuAndContent>
      <div class="w-full">
        <div class="font-bold text-lg mt-2">
          批量上传步骤：
        </div>
        <ol v-if="showStep">
          <li>
            批量上传之前，请先&nbsp;
            <el-link
              type="primary"
              @click="handleDownLoadTemp"
            >
              点击这里下载“数据批量导入模板”
            </el-link>。
          </li>
          <li>根据模板要求，填写数据模板内容。</li>
          <li>点击“数据批量导入”按钮，将已填写数据内容的模板上传至平台。</li>
          <li>为保证收到商品通报过程中，错漏反馈通知，<span class="color-red">请留下可联系的手机号</span>。</li>
          <li>上传后如需修改，请替换对应上传的文件；或删除后重新上传。</li>
          <li>
            遇到问题请
            <el-popover
              :width="260"
              placement="right"
              trigger="hover"
            >
              <template #reference>
                <el-link type="primary">
                  联系客服
                </el-link>
              </template>
              <template #default>
                <NuxtImg
                  alt="联系客服"
                  src="/resource/images/wx_customer_service.png"
                  width="230"
                />
                <div class="text-center pb-4">
                  微信扫码联系客服
                </div>
              </template>
            </el-popover>
            。
          </li>
        </ol>
        <el-divider
          class="cursor-pointer"
          @click="showStep = !showStep"
        >
          <span class="color-gray">{{ showStep ? '隐藏步骤提示' : ' 显示步骤提示' }}</span>
        </el-divider>
        <div class="flex gap-4 items-center">
          <div class="font-bold text-lg">
            已购买服务：
          </div>
          <el-radio-group
            v-if="reportAuth"
            v-model="serveSelect"
          >
            <el-radio
              value="1"
            >
              {{ orderContent }}
            </el-radio>
          </el-radio-group>
          <div
            v-else
            class="color-gray"
          >
            无
          </div>
        </div>
        <div class="flex mt-6 mb-4">
          <div class="font-bold text-lg mt-1 mr-8">
            业务办理：
          </div>
          <el-button
            class="mb-4"
            type="primary"
            @click="handleShowDialog"
          >
            数据批量导入
          </el-button>
        </div>
        <ClientOnly>
          <el-table
            :data="data"
            :height="tableHeight"
            current-row-key="fileRecordId"
          >
            <el-table-column
              label="文件名称"
              prop="fileName"
            >
              <template #default="scope">
                <div class="mt-2 flex gap-1">
                  <div class="i-ri:file-2-line mt-1.4 shrink-0" />
                  {{ scope.row.fileName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              prop="fileStateName"
              width="110"
            >
              <template #default="scope">
                <el-tag
                  :type="scope.row.fileState === 1?'success': scope.row.fileState === 0?'primary':'warning'"
                >
                  {{ !reportAuth && scope.row.fileState === 0 ? '未开通服务' :scope.row.fileStateName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="状态详情"
              prop="remark"
            >
              <template #default="scope">
                <div class="o-inline-2">
                  {{ scope.row.remark }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="上传日期"
              prop="uploadDate"
              width="110"
            >
              <template #default="scope">
                <div class="o-inline-2">
                  {{ scope.row.uploadDate }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label=""
              prop="qrCode"
              width="180"
            >
              <template #default="scope">
                <div class="flex gap-4">
                  <el-link
                    type="primary"
                    @click="handleDownLoad(scope.row.fileRecordId)"
                  >
                    下载
                  </el-link>
                  <el-upload
                    v-if="scope.row.fileState === 0"
                    v-model:file-list="fileList"
                    :action="useRuntimeConfig().public.apiBaseUrl + reportFileRecordUpdatePath"
                    :data="{
                      fileRecordId: scope.row.fileRecordId,
                    }"
                    :headers="{
                      Authorization: userStore().getToken(),
                    }"
                    :on-error="uploadFailed"
                    :on-success="uploadSuccess"
                    :show-file-list="false"
                    accept=".xls,.xlsx"
                    name="file"
                  >
                    <el-link
                      type="primary"
                    >
                      替换
                    </el-link>
                  </el-upload>
                  <el-popconfirm
                    v-if="scope.row.fileState === 0"
                    title="是否确认删除？"
                    @confirm="handleDel(scope.row.fileRecordId)"
                  >
                    <template #reference>
                      <el-link
                        type="danger"
                      >
                        删除
                      </el-link>
                    </template>
                  </el-popconfirm>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </ClientOnly>
        <el-dialog
          v-model="authDialogVisible"
          title="未开通产品信息通报服务"
          width="300"
        >
          <div class="flex flex-col items-center p-4">
            <div class="o-p">
              请扫码前往【微信小程序】，先选择开通产品信息通报服务。疑问请联系客服。
            </div>
            <NuxtImg
              src="/resource/images/makeFilm/appCode_from_websiteWxQr.png"
              alt="QR code"
              width="160"
              height="160"
              class="mt-4 block"
            />
          </div>
        </el-dialog>
        <el-dialog
          v-model="dialogVisible"
          title="条码成员身份校验"
          width="360"
          :lock-scroll="false"
        >
          <el-steps
            align-center
            :active="step"
            finish-status="success"
          >
            <el-step title="验证身份" />
            <el-step title="上传" />
          </el-steps>

          <div class="mt-4 px-4">
            <div v-show="step===0">
              <el-form
                ref="ruleFormRef"
                :model="ruleForm"
                :rules="rules"
                label-width="auto"
                status-icon
                style="max-width: 360px"
              >
                <el-form-item
                  label="条码卡号"
                  prop="barCodeCardNum"
                >
                  <el-input v-model="ruleForm.barCodeCardNum" />
                </el-form-item>
                <el-form-item
                  label="密码"
                  prop="barCodeCardPassword"
                >
                  <el-input v-model="ruleForm.barCodeCardPassword" />
                </el-form-item>
                <el-form-item
                  label="联系手机"
                  prop="phone"
                >
                  <el-input v-model="ruleForm.phone" />
                </el-form-item>
              </el-form>
              <div class="color-gray text-sm">
                <span class="color-red">*</span>为保证收到商品通报过程中，错漏反馈通知，请留下可联系的手机号。
              </div>
            </div>
            <div
              v-show="step!==0"
              class="o-flex-c pt-6 pb-10"
            >
              <el-upload
                v-model:file-list="fileList"
                :action="useRuntimeConfig().public.apiBaseUrl + reportFileRecordCreatePath"
                :data="{
                  userId: userStore().userId,
                }"
                :headers="{
                  Authorization: userStore().getToken(),
                }"
                :on-error="uploadFailed"
                :on-success="uploadSuccess"
                :show-file-list="false"
                accept=".xls,.xlsx"
                name="file"
              >
                <el-button
                  type="primary"
                >
                  选择文件
                </el-button>
              </el-upload>
            </div>
          </div>
          <template #footer>
            <div class="flex justify-end gap-4">
              <el-button @click="handleChanel">
                {{ step === 0 ? "取消" : "上一步" }}
              </el-button>
              <el-button
                type="primary"
                :disabled="step!==0"
                @click="submitForm"
              >
                下一步
              </el-button>
            </div>
          </template>
        </el-dialog>
      </div>
    </AppMenuAndContent>
  </div>
</template>

<style lang='scss' scoped>
ol > li::marker {
  @apply color-blue font-bold;
}
</style>
