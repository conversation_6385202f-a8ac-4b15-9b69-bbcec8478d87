<script lang="ts" setup>
import { v4 } from 'uuid'
import { useWebSocket } from '@vueuse/core'
import { useGetLoginQrCodeData } from '~/composables/apiArticle'

const isTimeOut = ref(false)
const useUserStore = userStore()

function generate16CharUUID() {
  // 生成 UUID
  const fullUUID = v4()
  // 去掉连字符并截取前16位
  return fullUUID.replace(/-/g, '').substring(0, 16)
}

const uuid = useState('uuid', () => generate16CharUUID())
// console.log(uuid.value)

// vueuse 的 webSocket 有 ssr 模式
const {
  status: wsStatus, data: wsData, send: wsSend, open: wsOpen, close: wsClose,
} = useWebSocket('wss://' + useRuntimeConfig().public.myProxyUrl + 'webSocket/' + uuid.value)

const { data, status, error, refresh, clear } = await useGetLoginQrCodeData({
  uuid: uuid.value,
})

onMounted(() => {
  if (import.meta.client) {
    setTimeout(() => {
      isTimeOut.value = true
      // wsClose()
    }, 3 * 60 * 1000)
  }
})

watch(wsData, () => {
  const d = JSON.parse(wsData.value)
  if (d.success) {
    useUserStore.userCode = d.data.userCode
    useUserStore.userId = d.data.userId
    useUserStore.token = d.data.Authorization
    console.log('userCode', useUserStore.userCode)
    console.log('userId', useUserStore.userId)
    console.log('token', useUserStore.token)
    // watch里面不能用await，所以不能用await navigateTo
    useRouter().push({
      path: '/',
      query: {
        msgType: 'success',
        msg: '登录成功',
      },
    })
  }
})

const refreshQr = () => {
  uuid.value = generate16CharUUID()
  refresh()
  wsOpen()
  isTimeOut.value = false
}
</script>

<template>
  <div class="f-h w-full o-flex-c o-bg-blue-light py-4">
    <div class="f-box bg-white p-4 flex flex-col items-center mx-auto rd-2">
      <div class="text-2xl pt-12">
        微信登录
      </div>
      <div
        class="f-qr-box relative mt-6"
      >
        <template v-if="wsStatus === 'OPEN'">
          <img
            v-if="wsStatus === 'OPEN'"
            :src="'data:image/png;base64,' + data"
            alt=""
            class="f-img"
          >
          <img
            alt=""
            class="f-img absolute top-0 left-0"
            src="/resource/images/scanFast.png"
          >
          <div
            v-if="isTimeOut"
            class="bg-black f-mask op-80 absolute top-0 left-0 cursor-pointer"
            @click="refreshQr"
          />
          <div
            v-if="isTimeOut"
            class="f-text absolute top-0 left-0 cursor-pointer"
          >
            <div class="i-ri:refresh-line color-white mx-auto mt-15 text-6xl" />
            <div class="text-sm color-white text-center mt-12">
              小程序码失效，点击重试
            </div>
          </div>
        </template>
      </div>
      <div class="text-sm mt-4">
        打开微信扫一扫，快速登录/注册
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.f-h{
  height: max(calc(100vh - 404px),400px);
}
.f-box {
  width: 330px;
  height: 370px;
}

.f-qr-box {
  $w: 180px;

  width: $w;
  height: $w;

  .f-mask, .f-text, .f-img {
    width: $w;
    height: $w;
  }

  .f-text{
    pointer-events: none; /* 禁止当前层的鼠标事件 */
  }
}
</style>
