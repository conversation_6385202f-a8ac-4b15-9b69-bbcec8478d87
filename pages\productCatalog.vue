<script lang="ts" setup>
import MasonryWall from '@yeger/vue-masonry-wall'
import { useEasyLightbox } from 'vue-easy-lightbox'
import { throttle } from 'throttle-debounce'

const scrollContainer = ref(null)
const bgImg = ref<string>()
const productList = ref<any[]>([])
const companyName = reactive({
  line1_str: '',
  line2_str: '',
  fontSize: 0,
})
const companyQr = ref('')
const companyCode = ref<string[]>([])
const CharE = 1.4 // 英文字符算多少个单位
const columnWidth = ref(300)
const page = 1
const iEmpty = ref(true)

onMounted(() => {
  if (import.meta.client) {
    columnWidth.value = (document.body.clientWidth / 2) * 0.8
  }
})

onUnmounted(() => {
  // 清理可能的定时器或者其他资源
  if (import.meta.client) {
    handleScrollThrottled.cancel()
  }
})

const { firstLine, secondLine, fontSize } = splitStringByUnits(
  // "深圳大苹果科技有限公司",
  '深圳大苹果科技有限公司',
  // "深圳大苹Apple果苹果小demo王女士先生",
  // "Apple demo company list Lts,Co. option city",
  // "深圳大苹Apple果苹果小demo王女士先生小王Co女苹果小王女士先生士先生科技有限公司",
)
companyName.line1_str = firstLine
companyName.line2_str = secondLine
companyName.fontSize = fontSize

companyQr.value = 'resource/demo/demo_qr_code.png'
companyCode.value = [companyQr.value]

const { show, onHide, visibleRef, indexRef, imgsRef } = useEasyLightbox({
  imgs: companyCode.value,
  initIndex: 0,
})

productList.value = [
  {
    id: 11,
    noData: true,
    title: '',
    img: '',
    url: '',
  },
  {
    id: 1,
    title: '12PM苹果16s手机电池',
    img: '/resource/demo/demo_xxx1.png',
    url: 'https://www.baidu.com',
  },
  {
    id: 2,
    title: '12PM苹果16s手机电1216s手机电果16s手机电',
    img: '/resource/demo/aaa2.png',
    url: 'https://www.baidu.com',
  },
  {
    id: 3,
    title: '12PM苹果16s手机电池',
    img: null,
    url: 'https://www.baidu.com',
  },
  {
    id: 4,
    title: '12PM苹果16s手机电池',
    img: null,
    url: 'https://www.baidu.com',
  },
  {
    id: 5,
    title: '12PM苹果16s手机电池',
    img: '/resource/demo/demo_xxx1.png',
    url: 'https://www.baidu.com',
  },
  {
    id: 6,
    title: '12PM苹果16s手机电池',
    img: null,
    url: 'https://www.baidu.com',
  },
  {
    id: 7,
    title: '12PM苹果16s手机电池',
    img: '/resource/demo/demo_xxx1.png',
    url: 'https://www.baidu.com',
  },
  {
    id: 8,
    title: '12PM苹果16s手机电池',
    img: null,
    url: 'https://www.baidu.com',
  },
]
bgImg.value = addImgUrlType(productList.value[1].img)

function calculateUnits(str: string): number {
  let units = 0
  for (const char of str) {
    // 判断是否为中文或全角符号
    if (/[\u3400-\u9FBF]/.test(char) || /[\uFF00-\uFFEF]/.test(char)) {
      units += 2 // 中文及全角符号占两个单位
    }
    else {
      units += CharE // 英文、数字及半角符号占一个单位
    }
  }
  return units
}

function splitStringByUnits(input: string) {
  const totalUnits = calculateUnits(input)

  const maxUnitsPerLine = 28
  const maxDifference = 2
  const maxWidth = 60
  let fontSize = 100 / 26
  // 初始化两行
  let firstLine = ''
  let secondLine = ''

  // 如果整体单位小于12，则直接返回原字符串
  if (totalUnits < 12) {
    firstLine = input
    fontSize = Math.max(fontSize, maxWidth / (totalUnits / 2))
  }
  else {
    // 计算第一行的最大单位数
    const firstLineMaxUnits = Math.min(
      maxUnitsPerLine,
      Math.ceil(totalUnits / 2),
    )
    // console.log("firstLineMaxUnits", firstLineMaxUnits);

    // 遍历输入字符串，分配到两行
    for (const char of input) {
      const currentUnit
        = /[\u3400-\u9FBF]/.test(char) || /[\uFF00-\uFFEF]/.test(char)
          ? 2
          : CharE

      if (calculateUnits(firstLine) + currentUnit <= firstLineMaxUnits) {
        firstLine += char
        // console.log("one firstLine", firstLine);
      }
      else {
        secondLine += char
        // console.log("one secondLine", secondLine);
      }
    }
    // console.log("one firstLine", firstLine);
    // console.log("one secondLine", secondLine);

    // 调整第二行，使其与第一行的单位差不超过2个单位
    /*    while (
      calculateUnits(secondLine) >
      calculateUnits(firstLine) + maxDifference
    ) {
      const lastChar = firstLine[firstLine.length - 1];
      firstLine = firstLine.slice(0, -1);
      secondLine = lastChar + secondLine;
      console.log("two firstLine", firstLine);
      console.log("two secondLine", secondLine);
    } */

    // 如果第二行超过最大单位数，则截断并添加省略号
    if (calculateUnits(secondLine) > maxUnitsPerLine) {
      secondLine = secondLine.slice(0, secondLine.length - 1) // 移除最后一个字符以腾出空间给省略号
      const ellipsis = '...'
      secondLine
        = secondLine.slice(
          0,
          secondLine.length - (calculateUnits(ellipsis) + 2),
        ) + ellipsis
    }
    fontSize = Math.max(fontSize, maxWidth / (calculateUnits(secondLine) / 2))
  }
  fontSize = Math.min(fontSize, 7)

  return { firstLine, secondLine, fontSize }
}

// 使用节流处理滚动事件
const handleScrollThrottled = throttle(2000, () => {
  if (scrollContainer.value) {
    const { scrollTop, clientHeight, scrollHeight } = scrollContainer.value
    if (scrollTop + clientHeight >= scrollHeight - 20) {
      // 当接近底部时触发
      console.log('加载更多')
    }
  }
})

const handleScroll = (event: Event) => {
  handleScrollThrottled(event)
}
</script>

<template>
  <div
    ref="scrollContainer"
    class="scroll-container w-full o-bg-gray-light"
    @scroll="handleScroll"
  >
    <div class="f-header">
      <div
        :style="{ backgroundImage: bgImg }"
        class="f-bg"
      />
      <div class="f-blur" />
      <div class="left-0 top-4 absolute">
        <div class="f-company-box flex justify-between items-end">
          <div class="flex font-bold">
            <div
              class="f-gray-square o-bg-gray mr-3 shrink-1"
              :style="{
                marginTop: companyName.fontSize * 0.11 + 'vw',
                height: companyName.fontSize + 'vw',
              }"
            />
            <div
              style="line-height: 120%"
              :style="{
                fontSize: companyName.fontSize + 'vw',
              }"
            >
              <div class="f-h-1">
                {{ companyName.line1_str }}
              </div>
              <div class="f-h-2">
                {{ companyName.line2_str }}
              </div>
            </div>
          </div>
          <div
            class="f-company-qr bg-white p-1 shrink-0"
            @click="show(0)"
          >
            <NuxtImg
              :src="companyQr"
              class="w-full h-full"
            />
          </div>
        </div>
        <div
          class="f-search flex items-center gap-2 bg-white rd-10 ml-7 mt-3 px-5 text-base"
        >
          <input
            class="text-xl mt-1"
            maxlength="30"
          >
          <div class="color-gray text-2xl shrink-0">
            <div class="i-ri-search-line" />
          </div>
        </div>
      </div>
    </div>
    <div class="mt--7 bg-white rd-3 px-7 pt-10 pb-10 relative z-1">
      <masonry-wall
        :items="productList"
        :ssr-columns="2"
        :column-width="columnWidth"
        :gap="16"
      >
        <template #default="{ item, index }">
          <div
            :key="item.id"
            class="f-product-box mb-4"
          >
            <div
              v-if="item.noData"
              class="pl-2 pt-4"
            >
              <NuxtImg
                src="/resource/images/noProduct.png"
                class="f-img"
              />
              <div class="o-font mt-2">
                <div class="of-t-1">
                  PRODUCT
                </div>
                <div class="of-t-2">
                  CATALOG
                </div>
                <div class="of-t-3">
                  产品目录
                </div>
              </div>
            </div>
            <template v-else>
              <NuxtImg
                v-if="item.img"
                class="f-img-box"
                :src="item.img"
              />
              <NuxtImg
                v-else
                class="f-img-box"
                src="/resource/images/NoImagesBg.png"
              />
              <div class="o-inline-4 mt-2">
                {{ item.title }}
              </div>
            </template>
          </div>
        </template>
      </masonry-wall>
      <!--      <div v-for="item in productList" :key="item.id" class="f-product-box">
        <div class="f-img-box relative overflow-hidden rd-1">
          <NuxtImg v-if="item.img" :src="item.img" class="f-img" />
          <NuxtImg v-else class="f-img" src="/resource/images/NoImagesBg.png" />
        </div>
        <div class="o-inline-2 mt-2">{{ item.title }}</div>
      </div> -->
    </div>
    <vue-easy-lightbox
      :imgs="companyCode"
      :index="indexRef"
      :visible="visibleRef"
      @hide="onHide"
    />
  </div>
</template>

<style lang="scss" scoped>
$w: 100vw;
$h: 49vw;

.scroll-container {
  height: 100vh; /* 设置容器高度 */
  overflow-y: auto; /* 开启垂直滚动 */
}

.f-header {
  position: relative;
  width: $w;
  height: $h;
  overflow: hidden;
}

.f-bg {
  width: $w;
  height: $h;
  background-color: $vf-default-color-gray;
  background-size: 160%;
  background-position-x: center;
  background-position-y: top;
  background-attachment: fixed;
  background-repeat: repeat;
  filter: blur(5vw) saturate(200%);
}

.f-blur {
  position: absolute;
  top: 0;
  left: 0;
  width: $w;
  height: $h;
  background-color: rgb(*********** / 70%);
}

.f-search {
  width: 77vw;
  height: 10.3vw;
}

input {
  width: 69vw;
  border: none;
  outline: none;
  background: none;
}

.f-product-box {
  $bw: 40.6vw;
  width: $bw;

  .f-img-box {
    width: $bw;
    //height: $bw;
    object-fit: cover;
  }
}

.f-company-box {
  width: 94vw;
}

.f-gray-square {
  width: 5.2vw;
}

.f-company-qr {
  $qw: 20vw;

  width: $qw;
  height: $qw;
}
</style>
