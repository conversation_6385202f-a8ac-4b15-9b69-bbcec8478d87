<script setup lang="ts">
const rotation = ref(0);

function handleOrientation(event: DeviceOrientationEvent) {
  const { gamma } = event; // gamma 表示设备左右倾斜角度
  if (gamma !== null) {
    rotation.value = -gamma / 2; // 调整灵敏度
  }
}

onMounted(() => {
  window.addEventListener("deviceorientation", handleOrientation);
});

onUnmounted(() => {
  window.removeEventListener("deviceorientation", handleOrientation);
});
</script>

<template>
  <div class="card-container" :style="{ transform: `rotate(${rotation}deg)` }">
    <div class="card">这是一个卡片</div>
  </div>
</template>

<style scoped lang="scss">
.card-container {
  /* 使用 vw 单位设置容器大小 */
  width: 60vw; /* 可以根据需要调整比例 */
  height: 90vw; /* 假设卡面宽高比为 2:3，可以根据实际内容调整 */
  perspective: 1000px;
}

.card {
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.5s;
}
</style>
