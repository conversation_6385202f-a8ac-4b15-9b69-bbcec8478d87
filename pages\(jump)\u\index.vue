<script setup lang="ts">
// 未注册中转页
import <PERSON><PERSON> from 'bowser-ultralight'

// 正式版为 release，体验版为 trial，开发版为 develop
const ENV_VERSION = 'release'
const { code } = useRoute().params
const isMobile = ref(true)

const myQuery = encodeURIComponent('smsop=' + code)
const url = 'weixin://dl/business/?appid=wxdee9d93adbb330b8&path=pages/index/index&query=' + myQuery + '&env_version=' + ENV_VERSION

onMounted(() => {
  if (import.meta.client) {
    const browser = Bowser.getParser(window.navigator.userAgent)
    isMobile.value = browser.isMobile()
  }
})
</script>

<template>
  <div class="w-full box-border o-bg-blue-light py-6 sm:py-20 px-6 sm:px-20">
    <div class="f-box mx-auto bg-white rd-2 px-8 sm:px-16 pt-10 pb-10 relative overflow-hidden o-shadow">
      <div
        class="f-code-loader"
        style="position: absolute; top: 0; right: 53px"
      />
      <div
        class="font-black absolute z-1"
        style="color:#F0F3F8FF;top:-40px;left:-10px;font-size:120px;"
      >
        注
      </div>
      <div class="text-2xl font-black flex items-baseline flex-wrap mt-8 relative z-2">
        <div class="text-4xl mr-0.5">
          注
        </div>
        <div>册条码系统成员，</div>
        <div>须知：</div>
      </div>
      <div class="font-bold mt-8">
        需要提交资料：
      </div>
      <ul>
        <li>
          填写系统成员注册申请表，加盖企业公章；
        </li>
        <li>
          企业营业执照或相关合法经营资质证明及复印件；
        </li>
        <li>
          汇款凭证复印件。
        </li>
      </ul>
      <div class="font-bold mt-6">
        注册成功发放材料：
      </div>
      <div class="o-p mt-2 pl-6">
        企业申请获得厂商识别代码后，中国物品编码中心会给企业发放《中国商品条码系统成员证书》、条码卡、发票、《中国商品条码系统成员用户手册》等材料。领取证书时，请核对企业信息是否正确，查看证书有效期，切记有效期满前办理续展手续。
      </div>
      <div class="font-bold mt-6">
        厂商识别代码使用期限：
      </div>
      <div class="o-p mt-2 pl-6">
        根据《商品条码管理办法》第二十八条规定：厂商识别代码的有效期为 2 年。如果需要继续使用，系统成员应在厂商识别代码有效期满前 3 个月内办理续展手续。逾期未办理续展手续的，注销其厂商识别代码和成员资格。
      </div>

      <div class="pt-6 pb-4">
        <div class="font-bold">
          相关知识：
        </div>
        <ul class="ml--4">
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/61"
              target="_blank"
            >未经核准注册的条码可以使用吗？</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/62"
              target="_blank"
            >条码可以转让给别人吗？</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/64"
              target="_blank"
            >企业可以使用已被注销的厂商识别代码吗？</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/63"
              target="_blank"
            >自身为集团公司的子公司，如何使用商品条码？</a>
          </li>
          <li>
            <a
              style="color:#838383"
              href="https://www.gs1helper.com/tutorials/45"
              target="_blank"
            >更多请前往小程序查看...</a>
          </li>
        </ul>
      </div>
      <a
        v-if="isMobile"
        class="mx-auto text-lg block bg-blue color-white font-bold py-6 px-10 rd-1 w-fit text-center"
        :href="url"
      >

        <div>点击前往小程序</div>
        <strong class="text-xl mt-2 font-bold">办理注册业务</strong>
      </a>
      <div
        v-else
        class="mx-auto w-fit text-center o-bg-blue-light rd-1 py-4 px-8"
      >
        <div>微信扫码，即可前往小程序</div>
        <strong class="text-xl mt-2">办理注册业务</strong>
      </div>
      <NuxtImg
        class="mx-auto block mt-4"
        src="/resource/images/jump/emailTransfer_unregistered.png"
        alt="小程序二维码"
        style="width: 200px;height: 200px;"
      />
      <div
        class="color-gray mt-2 text-sm text-center"
      >
        微信扫一扫
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.f-box {
  max-width: 600px;
}

li {
  @apply mb-1;

  &::marker {
    @apply color-blue font-bold leading-8;
  }

  a {
    color: #4f85ff;
  }
}

.f-code-loader {
  width: 45px;
  height: 40px;
  background: linear-gradient(
      #0000 calc(1 * 100% / 6),
      #000 0 calc(3 * 100% / 6),
      #0000 0
  ),
  linear-gradient(
      #0000 calc(2 * 100% / 6),
      #000 0 calc(4 * 100% / 6),
      #0000 0
  ),
  linear-gradient(
      #0000 calc(3 * 100% / 6),
      #000 0 calc(5 * 100% / 6),
      #0000 0
  );
  background-size: 10px 400%;
  background-repeat: no-repeat;
  animation: f-matrix-1555 1s infinite linear;
}

@keyframes f-matrix-1555 {
  0% {
    background-position: 0% 100%, 50% 100%, 100% 100%;
  }

  100% {
    background-position: 0% 0%, 50% 0%, 100% 0%;
  }
}
</style>
