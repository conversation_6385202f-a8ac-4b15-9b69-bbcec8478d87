<script setup lang="ts">
import { initializeKinesis } from '@amineyarman/kinesis'
import type { TabsPaneContext } from 'element-plus'
import { DesignImage } from '~/config/image'

const props = defineProps<{
  articleTypeId: number
  data: any
  articleList: any
}>()

// 添加数据防护的计算属性
const safeData = computed(() => {
  return props.data && Array.isArray(props.data) && props.data.length > 0 ? props.data : []
})

const safeItemList = computed(() => {
  return safeData.value[0]?.itemList || []
})

const safeArticleList = computed(() => {
  return props.articleList || []
})

let kinesisInitialized = false

onMounted(() => {
  if (import.meta.client) {
    try {
      initializeKinesis()
      kinesisInitialized = true
    } catch (error) {
      console.warn('Kinesis initialization failed:', error)
    }
  }
})

onBeforeUnmount(() => {
  // 清理 Kinesis 相关的事件监听器
  if (import.meta.client && kinesisInitialized) {
    try {
      // 移除所有 kinesis 相关的元素属性和事件监听器
      const kinesisElements = document.querySelectorAll('[data-kinesisdistance-item]')
      kinesisElements.forEach(el => {
        el.removeAttribute('data-kinesisdistance-item')
        el.removeAttribute('data-ks-strength')
        el.removeAttribute('data-ks-transform')
        el.removeAttribute('data-ks-interaction')
        el.removeAttribute('data-ks-startdistance')
        el.removeAttribute('data-ks-duration')
      })
    } catch (error) {
      console.warn('Kinesis cleanup failed:', error)
    }
  }
})

const id = ref(props.articleTypeId)
const emit = defineEmits([
  'update-id',
])

watch(id, () => {
  emit('update-id', id.value)
})

const getHref = (d: any) => {
  if (d?.articleUrl) {
    return d.articleUrl
  }
  else if (d?.articleId) {
    return `/tutorials/${d.articleId}`
  }
  return '#'
}

const handleTabsClick = (tab: TabsPaneContext, event: Event) => {
  // 添加错误处理
  try {
    // 处理标签点击逻辑
  } catch (error) {
    console.warn('Tab click handler error:', error)
  }
}
</script>

<template>
  <div
    class="text-center text-3xl py-8 mt-4"
  >
    条码规范 · 知识学习
  </div>
  <el-tabs
    v-model="id"
    class="o-flex-c"
    @tab-click="handleTabsClick"
  >
    <el-tab-pane
      v-for="tab in data[0]?.itemList"
      :key="tab.articleTypeId"
      :label="tab.typeName"
      :name="tab.articleTypeId"
    />
  </el-tabs>
  <transition-group
    name="fade"
    class="f-article-box w-full mt-10 grid justify-items-center gap-8 op-100"
    tag="div"
  >
    <a
      v-for="item in articleList"
      :key="item.articleId"
      class="f-article-item cursor-pointer o-transition-fast hover:color-blue"
      :href="getHref(item)"
      target="_blank"
    >
      <div
        class="f-article-img overflow-hidden rd-2 op-90 hover:op-100"
        data-kinesisdistance-item
        data-ks-strength="15"
        data-ks-transform="scale"
        data-ks-interaction="attraction"
        data-ks-startdistance="120"
        data-ks-duration="500"
      >
        <NuxtImg
          :src="item.imageUrl ? item.imageUrl: DesignImage.tutorials.defaultMainImg"
          alt=""
        />
      </div>
      <div class="o-inline-2 mt-4">
        {{ item.articleTitle }}
      </div>
    </a>
  </transition-group>
</template>

<style scoped lang="scss">
.f-article-box {
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
}

.f-article-item {
  width: 260px;
  height: 230px;

  .f-article-img {
    width: 100%;
    height: 180px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

.fade-enter-active {
  transition: all 2s ease;
}
</style>
