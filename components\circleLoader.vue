<script setup lang="ts">

</script>

<template>
  <div class="container">
    <span />
    <span />
    <span />
    <span />
  </div>
</template>

<style scoped lang="scss">
.container {
  position: absolute;
  //top: 50%;
  //left: 50%;
  border-radius: 50%;
  height: 60px;
  width: 60px;
  animation: rotate_3922 1.2s linear infinite;
  background-color: $vf-primary;
  background-image: linear-gradient($vf-primary, #84cdfa, #6bc5ff);
}

.container span {
  position: absolute;
  border-radius: 50%;
  height: 100%;
  width: 100%;
  background-color: $vf-primary;
  background-image: linear-gradient($vf-primary, #84cdfa, #6bc5ff);
}

.container span:nth-of-type(1) {
  filter: blur(3px);
}

.container span:nth-of-type(2) {
  filter: blur(6px);
}

.container span:nth-of-type(3) {
  filter: blur(15px);
}

.container span:nth-of-type(4) {
  filter: blur(30px);
}

.container::after {
  content: "";
  position: absolute;
  top: 6px;
  left: 6px;
  right: 6px;
  bottom: 6px;
  background-color: #fff;
  border: solid 3px #ffffff;
  border-radius: 50%;
}

@keyframes rotate_3922 {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
