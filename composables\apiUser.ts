// 参数接口
import { fetchApi } from '~/composables/api'

export interface GetUserServerParams {
  certificationId?: number
  userId: number
}

export interface GetUserServerResData {
  barCodeCardNum: string
  barCodeCardPassword: string
  phone: string
  qrAuth: boolean
  qrOrderInfo: {
    capacity: number
    certificationId: number
    createdDate: Record<string, unknown>
    expiryDate: Record<string, unknown>
    flow: number
    orderCode: string
    startDate: Record<string, unknown>
    tempType: number
  }
  reportAuth: boolean
  reportOrderInfo: {
    createdDate: Record<string, unknown>
    orderCode: string
    orderContent: string
    serverType: number
  }
  userId: number
}

// 响应接口
export interface GetUserServerRes {
  data: GetUserServerResData
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 获取用户的购买服务信息
 * @param {object} params cmd
 * @param {number} params.certificationId 认证id
 * @param {number} params.userId 用户id
 * @returns
 */
export const getUserServerInfoApi = (params: GetUserServerParams, options?: any) =>
  fetchApi<GetUserServerRes>('/user/getUserServerInfo', {
    method: 'POST',
    body: params,
    ...options,
  })

// 参数接口
export interface UpdateUserPhoneParams {
  barCodeCardNum?: string
  barCodeCardPassword?: string
  phone: string
  userId: number
}

export interface UpdateUserPhoneResData {
  barCodeCardNum: string
  barCodeCardPassword: string
  phone: string
  userCode: string
  userId: number
}

// 响应接口
export interface UpdateUserPhoneRes {
  data: UpdateUserPhoneResData
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 修改用户手机号
 * @param {object} params cmd
 * @param {string} params.phone 电话
 * @param {number} params.userId 用户id
 * @returns
 */
export const updateUserPhoneApi = (params: UpdateUserPhoneParams, options?: any) =>
  fetchApi<UpdateUserPhoneResData>('/user/updateUserPhone', {
    method: 'POST',
    body: params,
    ...options,
  })
