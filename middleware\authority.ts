import { getUserServerInfoApi } from '~/composables/apiUser'

export default defineNuxtRouteMiddleware((to, from) => {
  switch (to.path) {
    case '/miniShop':
    case '/miniShop/create':
      console.log('eeeeee')
      getUserServerInfoApi({ userId: Number(userStore().userId) }).then((res) => {
        console.log('hhhh')
        return res.data.qrAuth
      }).catch(() => {
        console.log('gggggggg')
        return false
      })
  }
})
