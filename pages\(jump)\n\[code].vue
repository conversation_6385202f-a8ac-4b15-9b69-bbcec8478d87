<script setup lang="ts">
// 新注册中转页
import <PERSON><PERSON> from 'bowser-ultralight'
import { createQrCode<PERSON>pi } from '~/composables/apiJump'

// 正式版为 release，体验版为 trial，开发版为 develop
const ENV_VERSION = 'release'
const { code } = useRoute().params
const isMobile = ref(true)

const { data } = await createQrCode<PERSON>pi({
  dataCode: code,
})
const myQuery = encodeURIComponent('smsop=' + code)
const url = 'weixin://dl/business/?appid=wxdee9d93adbb330b8&path=pages/index/index&query=' + myQuery + '&env_version=' + ENV_VERSION

onMounted(() => {
  if (import.meta.client) {
    const browser = Bowser.getParser(window.navigator.userAgent)
    isMobile.value = browser.isMobile()
  }
})
</script>

<template>
  <div class="w-full box-border o-bg-blue-light py-6 sm:py-20 px-6 sm:px-20">
    <div class="f-box mx-auto bg-white rd-2 px-8 sm:px-16 pt-10 pb-10 relative overflow-hidden o-shadow">
      <div
        class="f-code-loader"
        style="position: absolute; top: 0; right: 53px"
      />
      <div
        class="font-black absolute z-1"
        style="color:#F0F3F8FF;top:-40px;left:-10px;font-size:120px;"
      >
        新
      </div>
      <div class="text-2xl font-black flex items-baseline flex-wrap mt-8 relative z-2">
        <div class="text-4xl mr-0.5">
          新
        </div>
        <div>条码系统成员，</div>
        <div>接下来的步骤：</div>
      </div>
      <ol class="ml--4">
        <li>确认自身需要申请条形码的商品数量。</li>
        <li>
          用厂商识别码去申请制作相应的商品条码电子胶片，取得合规的完整条码编号及电子胶片。
          <ul class="ml--6">
            <li>条码编码需要符合规范。</li>
            <li>条码需要进行查重保证唯一性。</li>
            <li>电子胶片需要符合商用规范。</li>
            <li>请确保上诉无误后才进行包装印刷，避免造成投放市场后的重大损失。</li>
          </ul>
        </li>
        <li>赋码后的产品进行信息通报。只有成功通报后的产品才能被扫码识别出对应的信息。</li>
      </ol>
      <div class="pt-6 pb-4">
        <div class="font-bold">
          相关知识：
        </div>
        <ul class="ml--4">
          <li>
            <a
              href="https://mp.weixin.qq.com/s/rHEyfOHWQ3lnNh15_OZ9wg"
              target="_blank"
            >条码胶片制作必备信息，你知道吗？</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/38"
              target="_blank"
            >13位码和14位码的区别</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/44"
              target="_blank"
            >如何为标准组合包装的零售商品编制代码？</a>
          </li>
          <li>
            <a
              href="https://www.gs1helper.com/tutorials/45"
              target="_blank"
            >如何为混合组合包装的零售商品编码？</a>
          </li>
          <li>
            <a
              style="color:#838383"
              href="https://www.gs1helper.com/tutorials/45"
              target="_blank"
            >更多请前往小程序查看...</a>
          </li>
        </ul>
      </div>
      <a
        v-if="isMobile"
        class="mx-auto text-lg block bg-blue color-white font-bold py-6 px-10 rd-1 w-fit text-center"
        :href="url"
      >

        <div>点击前往小程序</div>
        <strong class="text-xl mt-2 font-bold">激活、制作条码、办理通报</strong>
      </a>
      <div
        v-else
        class="mx-auto w-fit text-center o-bg-blue-light rd-1 py-4 px-8"
      >
        <div>微信扫码，即可前往小程序</div>
        <strong class="text-xl mt-2">激活、制作条码、办理通报</strong>
      </div>
      <NuxtImg
        class="mx-auto block mt-4"
        :src="'data:image/png;base64,'+data?.data"
        alt="小程序二维码"
        style="width: 200px;height: 200px;"
      />
      <div
        class="color-gray mt-2 text-sm text-center"
      >
        微信扫一扫
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.f-box {
  max-width: 600px;
}

li {
  @apply mb-1;

  &::marker {
    @apply color-blue font-bold leading-8;
  }

  a {
    color: #4f85ff;
  }
}

.f-code-loader {
  width: 45px;
  height: 40px;
  background: linear-gradient(
      #0000 calc(1 * 100% / 6),
      #000 0 calc(3 * 100% / 6),
      #0000 0
  ),
  linear-gradient(
      #0000 calc(2 * 100% / 6),
      #000 0 calc(4 * 100% / 6),
      #0000 0
  ),
  linear-gradient(
      #0000 calc(3 * 100% / 6),
      #000 0 calc(5 * 100% / 6),
      #0000 0
  );
  background-size: 10px 400%;
  background-repeat: no-repeat;
  animation: f-matrix-1555 1s infinite linear;
}

@keyframes f-matrix-1555 {
  0% {
    background-position: 0% 100%, 50% 100%, 100% 100%;
  }

  100% {
    background-position: 0% 0%, 50% 0%, 100% 0%;
  }
}
</style>
