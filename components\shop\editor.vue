<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { Delete, Minus, Plus, ZoomIn, Download } from '@element-plus/icons-vue'
import OSS from 'ali-oss'
import { useEasyLightbox } from 'vue-easy-lightbox'
import Editor from '@tinymce/tinymce-vue'
import { getGPCidPath, goodsCreateApi, goodsLoadApi, goodsUpdateApi } from '~/composables/apiMiniShop'
import { ossStore } from '~/stores/ossStore'
import { getFileSizeStr, getRandomFileName, replaceDomain } from '~/utils'
import { TINYMCE_KEY } from '~/composables/constant'
import { useShopCreateTiny } from '~/composables/forComponent/useShopCreateTiny'
import { goodsEditorStore } from '~/stores/goodsEditorStore'
import { downloadOssFile } from '~/composables'

defineExpose({
  submitForm,
})

const { isCreate, dialogVisible, disableEdit = false } = defineProps<{
  isCreate: boolean
  dialogVisible: boolean
  disableEdit?: boolean
}>()
const emit = defineEmits([
  'update:dialogVisible',
  'success',
])

type Attachment = {
  name: string
  url: string
  size: number
}

const VIDEO_MAX_SIZE = 200 * 1024 * 1024 // 视频最大文件大小为200MB
const ATTACHMENT_MAX_SIZE = 200 * 1024 * 1024 // 最大文件大小为200MB

const videoUrl = ref('')
const ossClient = ref()
const imgList = ref<string[]>([])
const attachmentList = ref<Attachment[]>([])
const standardInputs = ref<string[]>([''])
const standardNumberInputs = ref<string[]>([''])
const executeYear = ref<string[]>([''])
const videoUploading = ref(false)
const attachmentUploading = ref(false)

/* attachmentList.value = [{
  name: '说明书.pdf',
  url: 'https://oss.yun.yunyouyun.cn/mini-shop/attachment/2023/07/04/%E6%9D%82%E8%B4%A7%E6%8A%A5%E5%91%8A.pdf',
  size: 12345678,
}] */

const useGoodsEditorStore = goodsEditorStore()
const { goodId } = storeToRefs(useGoodsEditorStore)
const useStepStore = miniShopCreateStepStore()
const { activeStep } = storeToRefs(useStepStore)
const { contentHTML, editInit } = useShopCreateTiny(ossClient)
const { show, onHide, visibleRef, indexRef, imgsRef } = useEasyLightbox({
  imgs: imgList.value,
  initIndex: 0,
})

interface RuleForm {
  barCode: string
  barType: 'EAN' | 'UPC-A' | 'ITF'
  brandName: string
  certificationId?: number
  commonName: string
  companyPrice?: number
  currency?: '人名币' | '美元' | '欧元'
  goodsDescription: string
  goodsName: string
  goodsType: '在产' | '不在产'
  gpcTypeArr: string[]
  gpcType: string
  imageUrl?: string[]
  isPrivary: boolean
  marketDate?: string
  netContent: string
  netContentUnit: string
  productFeatures?: string
  spec: string
  video: string
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  // 写死就是不用提现
  // certificationId: 0,
  barCode: '',
  barType: 'EAN',
  brandName: '',
  commonName: '',
  companyPrice: 0,
  currency: '人名币',
  goodsDescription: '',
  goodsName: '',
  goodsType: '在产',
  gpcTypeArr: [],
  gpcType: '',
  imageUrl: [],
  isPrivary: false,
  marketDate: '',
  netContent: '',
  netContentUnit: '',
  productFeatures: '',
  spec: '',
  video: '',
})

const rules = reactive<FormRules<RuleForm>>({
  barCode: [
    { required: true, message: '请输入正确的条码', trigger: 'change' },
  ],
  brandName: [
    { required: true, message: '请输入品牌名', trigger: 'change' },
  ],
  goodsName: [
    { required: true, message: '请输入产品名称', trigger: 'change' },
  ],
  commonName: [
    { required: true, message: '请输入产品通用名', trigger: 'change' },
  ],
  gpcTypeArr: [
    { required: true, message: '请选择产品（GPC）分类', trigger: 'change' },
  ],
  netContent: [
    { required: true, message: '请输入净含量', trigger: 'change' },
  ],
  netContentUnit: [
    { required: true, message: '请输入净含量单位', trigger: 'change' },
  ],
  spec: [
    { required: true, message: '请输入规格', trigger: 'change' },
  ],
  goodsDescription: [
    { required: true, message: '请输入产品描述', trigger: 'change' },
  ],
  goodsType: [
    { required: true, message: '请选择在产/不在产', trigger: 'change' },
  ],
})

const findCodePath = (data: any[], targetValue: string, path: string[] = []): string[] | null => {
  for (const item of data) {
    path.push(item.value)
    if (item.value === targetValue) {
      return path
    }
    if (item.children && item.children.length > 0) {
      const result = findCodePath(item.children, targetValue, path)
      if (result) {
        return result
      }
    }
    path.pop()
  }
  return null
}

const {
  data: gpcData, status: gpcStatus, error: gpcError, refresh: gpcRefresh, clear: gpcClear,
} = await useFetch(getGPCidPath)

function init() {
  ruleForm.barCode = ''
  ruleForm.barType = 'EAN'
  ruleForm.brandName = ''
  ruleForm.commonName = ''
  ruleForm.companyPrice = 0
  ruleForm.currency = '人名币'
  ruleForm.goodsDescription = ''
  ruleForm.goodsName = ''
  ruleForm.goodsType = '在产'
  ruleForm.gpcTypeArr = []
  ruleForm.gpcType = ''
  ruleForm.imageUrl = []
  ruleForm.isPrivary = false
  ruleForm.marketDate = ''
  ruleForm.netContent = ''
  ruleForm.netContentUnit = ''
  ruleForm.productFeatures = ''
  ruleForm.spec = ''
  ruleForm.video = ''

  standardInputs.value = ['']
  standardNumberInputs.value = ['']
  executeYear.value = ['']
  imgList.value = []
  videoUrl.value = ''
  contentHTML.value = ''
  attachmentList.value = []
}

function getData(id: number) {
  goodsLoadApi({ goodsId: id }).then((res) => {
    const d = res.data
    console.log(d)
    ruleForm.barCode = d.barCode
    ruleForm.barType = d.barType
    ruleForm.brandName = d.brandName
    ruleForm.commonName = d.commonName
    ruleForm.companyPrice = d.companyPrice
    ruleForm.currency = d.currency
    ruleForm.goodsDescription = d.goodsDescription
    ruleForm.goodsName = d.goodsName
    ruleForm.goodsType = d.goodsType
    ruleForm.isPrivary = d.isPrivary
    ruleForm.marketDate = d.marketDate
    ruleForm.netContent = d.netContent
    ruleForm.netContentUnit = d.netContentUnit
    ruleForm.productFeatures = d.productFeatures
    ruleForm.spec = d.spec
    ruleForm.gpcType = d.gpcType
    ruleForm.video = d.video
    standardNumberInputs.value = d.standardNumber.split(',')
    standardInputs.value = d.executeStandard.split(',')
    executeYear.value = d.executeYear.split(',')
    if (d.attachment) {
      try {
        attachmentList.value = JSON.parse(d.attachment)
      }
      catch (e) {
        console.log(e)
      }
    }

    // executeStandard.value = d.
    ruleForm.gpcTypeArr = findCodePath(gpcData.value, d.gpcType)
    if (d.sliderImages?.length > 0) {
      ruleForm.imageUrl = d.sliderImages.split(',') // 轮播图，多张图片用“，“隔开
      console.log(ruleForm.imageUrl)
      ruleForm.imageUrl.forEach((item) => {
        // imgList.value.push(replaceDomain(ossClient.value.signatureUrl(item)))
        imgList.value.push(ossClient.value.signatureUrl(item))
      })
    }
    else {
      ruleForm.imageUrl = []
    }
    if (ruleForm.video) {
      videoUrl.value = ossClient.value.signatureUrl(ruleForm.video)
    }
    contentHTML.value = updateImageSrc(d.goodsContent)
  })
}
watch(() => dialogVisible, () => {
  if (!dialogVisible) {
    init()
    ruleFormRef.value?.resetFields()
  }
  if (dialogVisible && !isCreate) {
    getData(goodId.value)
  }
}, {
  immediate: true,
})

async function submitForm() {
  if (!ruleFormRef.value) return
  await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      // console.log('submit!')
      const params = {
        attachment: JSON.stringify(attachmentList.value),
        barCode: ruleForm.barCode,
        barType: ruleForm.barType,
        brandName: ruleForm.brandName,
        commonName: ruleForm.commonName,
        companyPrice: ruleForm.companyPrice,
        currency: ruleForm.currency,
        executeStandard: standardInputs.value.join(','),
        executeYear: executeYear.value.join(','),
        goodsDescription: ruleForm.goodsDescription,
        goodsName: ruleForm.goodsName,
        goodsType: ruleForm.goodsType,
        gpcType: ruleForm.gpcTypeArr[ruleForm.gpcTypeArr.length - 1],
        imageUrl: ruleForm.imageUrl,
        isPrivary: false,
        marketDate: ruleForm.marketDate,
        netContent: ruleForm.netContent,
        netContentUnit: ruleForm.netContentUnit,
        productFeatures: ruleForm.productFeatures,
        spec: ruleForm.spec,
        video: ruleForm.video,
        standardNumber: standardNumberInputs.value.join(','),
        goodsContent: contentHTML.value,
        userId: Number(userStore().userId),
      }

      if (isCreate) {
        goodsCreateApi(params).then(() => {
          activeStep.value = 3
          emit('success')
        })
      }
      else {
        goodsUpdateApi({
          goodsId: goodId.value,
          ...params,
        }).then(() => {
          emit('update:dialogVisible', false)
          emit('success')
        })
      }
    }
    else {
      console.log('表单校验未通过', fields)
      try {
        Object.keys(fields).forEach((key) => {
          fields[key].forEach((item) => {
            ElMessage.error(item.message)
          })
        })
      }
      catch (e) {
        console.log(e)
      }
    }
  })
}

const restaurants = ref([
  { value: 'GB' },
  { value: 'GB/Z' },
  { value: 'NY' },
  { value: 'GB/T' },
  { value: 'GBZ/T' },
  { value: 'NY/T' },
])
const createFilter = (queryString: string) => {
  return (restaurant: any) => {
    return (
      restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
    )
  }
}
const querySearch = (queryString: string, cb: any) => {
  const results = queryString
    ? restaurants.value.filter(createFilter(queryString))
    : restaurants.value
  // call callback function to return suggestions
  cb(results)
}
const handleSelect = (item: any) => {
  console.log(item)
}

const handleAdd = () => {
  if (standardInputs.value.length < 5) {
    standardInputs.value.push('')
    standardNumberInputs.value.push('')
    executeYear.value.push('')
  }
}

const removeInput = (index: number) => {
  standardInputs.value.splice(index, 1)
  standardNumberInputs.value.splice(index, 1)
  executeYear.value.splice(index, 1)
}

const useOssStore = ossStore()
const imgFileInput = ref<HTMLInputElement>()
const videoFileInput = ref<HTMLInputElement>()
const attachmentFileInput = ref<HTMLInputElement>()
const MAX_IMG_COUNT = 5

const handleAddImg = () => {
  if (imgFileInput.value && imgList.value.length < MAX_IMG_COUNT) {
    imgFileInput.value.click() // 触发文件选择对话框
  }
}
const handleAddVideo = () => {
  if (videoFileInput.value && !videoUploading.value) {
    videoFileInput.value.click() // 触发文件选择对话框
  }
}
const handleAddAttachment = () => {
  if (attachmentFileInput.value && !attachmentUploading.value) {
    attachmentFileInput.value.click() // 触发文件选择对话框
  }
}

const handleImgFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    if (target.files !== null) {
      const file = target.files[0]
      const maxSize = 2 * 1024 * 1024 // 最大文件大小为2MB
      if (file.size > maxSize) {
        ElMessageBox.alert(`文件大小超过${maxSize}MB，请压缩后上传`)
      }
      else {
        const filePathAndName = getRandomFileName(OssFilePath.goodsImg, file.name)
        ossClient.value.put(filePathAndName, file).then((res) => {
          // console.log('put success: %j', res)
          ruleForm.imageUrl!.push(res.name)
          // imgList.value.push(replaceDomain(ossClient.value.signatureUrl(res.name)))
          imgList.value.push(ossClient.value.signatureUrl(res.name))
        }).finally(() => {
          imgFileInput.value!.value = ''
        })
      }
    }
  }
}

const handleVideoFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    if (target.files !== null) {
      const file = target.files[0]
      if (file.size > VIDEO_MAX_SIZE) {
        ElMessageBox.alert(`文件大小超过${getFileSizeStr(VIDEO_MAX_SIZE)}MB，请压缩后上传`)
      }
      else {
        const filePathAndName = getRandomFileName(OssFilePath.goodsVideo, file.name)
        videoUploading.value = true
        ossClient.value.put(filePathAndName, file).then((res) => {
          // console.log('put success: %j', res)
          ruleForm.video = res.name
          // imgList.value.push(replaceDomain(ossClient.value.signatureUrl(res.name)))
          videoUrl.value = ossClient.value.signatureUrl(res.name)
        }).finally(() => {
          videoFileInput.value!.value = ''
          videoUploading.value = false
        })
      }
    }
  }
}

const handleAttachmentFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    if (target.files !== null) {
      const file = target.files[0]
      if (file.size > ATTACHMENT_MAX_SIZE) {
        ElMessageBox.alert(`文件大小超过${getFileSizeStr(ATTACHMENT_MAX_SIZE)}MB，请压缩后上传`)
      }
      else {
        const filePathAndName = getRandomFileName(OssFilePath.attachment, file.name)
        attachmentUploading.value = true
        ossClient.value.put(filePathAndName, file).then((res) => {
          // console.log('put success: %j', res)
          attachmentList.value.push({
            name: file.name,
            url: filePathAndName,
            size: file.size,
          })
        }).finally(() => {
          attachmentFileInput.value!.value = ''
          attachmentUploading.value = false
        })
      }
    }
  }
}

onMounted(() => {
  if (import.meta.client) {
    useOssStore.getCredentials().then(() => {
      // const OSS = require('ali-oss')
      ossClient.value = new OSS({
        // 将<YOUR_BUCKET>设置为OSS Bucket名称。
        bucket: useOssStore.BUCKET,
        // 将<YOUR_REGION>设置为OSS Bucket所在地域，例如region: 'oss-cn-hangzhou'。
        region: useOssStore.REGION,
        accessKeyId: useOssStore.accessKeyId,
        accessKeySecret: useOssStore.accessKeySecret,
        stsToken: useOssStore.securityToken,
      })
    }).catch((err) => {
      console.log('err', err)
      ElMessage.error('获取OSS凭证失败')
    })
  }
})

const handleDelImg = (index: number) => {
// 删除imgList的index元素
  imgList.value.splice(index, 1)
  ruleForm.imageUrl!.splice(index, 1)
}

const handleDelVideo = () => {
  videoUrl.value = ''
  ruleForm.video = ''
}

function refreshOssImg(urlStr: string): string {
  const url = new URL(urlStr)
  return replaceDomain(ossClient.value.signatureUrl(url.pathname))
}

function updateImageSrc(input: string): string {
  // 使用正则表达式匹配 img 标签中的 src 属性
  const imgSrcRegex = /<img\s+[^>]*src="([^"]*)"[^>]*>/g

  // 通过 replace 函数替换每一个匹配的 src 地址
  return input.replace(imgSrcRegex, (match, src) => {
    // 使用 refreshOssImg 函数更新 src 地址
    const updatedSrc = refreshOssImg(src)

    // 将更新后的 src 地址替换回原来的 img 标签
    return match.replace(src, updatedSrc)
  })
}

function handleDownloadAttachment(data: Attachment) {
  downloadOssFile(ossClient.value, data.url, data.name)
}

function handleDelAttachment(url: string) {
  attachmentList.value = attachmentList.value.filter(item => item.url !== url)
}
</script>

<template>
  <div class="px-4">
    <div class="flex mb-3">
      <div
        class="f-left-babel"
      >
        商品主图
      </div>
      <transition-group
        mode="out-in"
        name="fade"
        tag="div"
        class="flex gap-4 flex-wrap"
      >
        <div
          v-for="(item, index) in imgList"
          :key="item"
          class="f-upload-img relative overflow-hidden rd-2"
        >
          <el-image
            :src="item"
            style="width: 100%; height: 100%;"
            fit="cover"
          />
          <div class="f-img-mask w-full h-full absolute top-0 left-0 op-0 hover:op-100 o-transition-fast o-flex-c gap-4">
            <el-icon
              color="white"
              size="20"
              class=" cursor-pointer"
              @click="show(index)"
            >
              <ZoomIn />
            </el-icon>
            <el-icon
              v-if="!disableEdit"
              color="white"
              size="20"
              class=" cursor-pointer"
              @click="handleDelImg(index)"
            >
              <Delete />
            </el-icon>
          </div>
        </div>
        <div
          v-show="imgList.length < MAX_IMG_COUNT && !disableEdit"
          key="f-img-input"
          class="f-img-input o-flex-c o-bg-blue-light o-font-color-secondary"
          @click="handleAddImg"
        >
          <div class="i-ri:add-large-line text-2xl" />
        </div>
        <input
          key="img-file-input"
          ref="imgFileInput"
          class="hidden"
          type="file"
          accept="image/*"
          @change="handleImgFileChange"
        >
      </transition-group>
    </div>
    <el-form
      ref="ruleFormRef"
      style="max-width: 600px"
      :model="ruleForm"
      :rules="rules"
      label-width="auto"
      status-icon
    >
      <el-form-item
        label="商品条码"
        prop="barCode"
      >
        <el-input
          v-model="ruleForm.barCode"
          :disabled="disableEdit"
        />
      </el-form-item>

      <el-form-item
        label="品牌名称"
        prop="brandName"
      >
        <el-input
          v-model="ruleForm.brandName"
          :disabled="disableEdit"
        />
      </el-form-item>
      <el-form-item
        label="产品名称"
        prop="goodsName"
      >
        <el-input
          v-model="ruleForm.goodsName"
          :disabled="disableEdit"
        />
      </el-form-item>
      <el-form-item
        label="产品通用名"
        prop="commonName"
      >
        <el-input
          v-model="ruleForm.commonName"
          :disabled="disableEdit"
        />
      </el-form-item>
      <el-form-item
        label="产品（GPC）分类"
        prop="gpcTypeArr"
      >
        <el-cascader
          v-model="ruleForm.gpcTypeArr"
          placeholder="可输入查询"
          :options="gpcData"
          style="width: 500px"
          :disabled="disableEdit"
          filterable
          clearable
          :show-all-levels="false"
        />
      </el-form-item>
      <el-form-item
        label="产品特征"
        prop="productFeatures"
      >
        <el-input
          v-model="ruleForm.productFeatures"
          :disabled="disableEdit"
        />
      </el-form-item>
      <div class="flex">
        <el-form-item
          label="净含量"
          prop="netContent"
        >
          <el-input
            v-model="ruleForm.netContent"
            :disabled="disableEdit"
          />
        </el-form-item>
        <el-form-item
          class="ml-2"
          prop="netContentUnit"
        >
          <el-input
            v-model="ruleForm.netContentUnit"
            :disabled="disableEdit"
            placeholder="单位"
          />
        </el-form-item>
      </div>
      <el-form-item
        label="规格"
        prop="spec"
      >
        <el-input
          v-model="ruleForm.spec"
          :disabled="disableEdit"
        />
      </el-form-item>
      <el-form-item
        label="产品描述"
        prop="goodsDescription"
      >
        <el-input
          v-model="ruleForm.goodsDescription"
          :disabled="disableEdit"
        />
      </el-form-item>
      <el-form-item
        label="产品状态"
        prop="resource"
      >
        <el-radio-group
          v-model="ruleForm.goodsType"
          :disabled="disableEdit"
        >
          <el-radio value="在产">
            在产
          </el-radio>
          <el-radio value="不在产">
            不在产
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="（预计）上市日期"
        prop="marketDate"
      >
        <el-date-picker
          v-model="ruleForm.marketDate"
          :disabled="disableEdit"
          value-format="YYYY-MM-DD"
          type="date"
        />
      </el-form-item>
      <el-form-item
        label="企业定价"
        prop="companyPrice"
      >
        <el-input
          v-model="ruleForm.companyPrice"
          :disabled="disableEdit"
          style="max-width: 600px"
          placeholder="Please input"
          type="number"
          max="*********"
        >
          <template #append>
            <el-select
              v-model="ruleForm.currency"
              style="width: 115px"
              :disabled="disableEdit"
            >
              <el-option
                label="人民币"
                value="人民币"
              />
              <el-option
                label="美元"
                value="美元"
              />
              <el-option
                label="欧元"
                value="欧元"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        label="产品执行标准"
        prop="executeStandard"
      >
        <div
          v-for="(item, index) in standardInputs"
          :key="index"
          class="flex gap-2 mb-6"
        >
          <el-autocomplete
            v-model="standardInputs[index]"
            :fetch-suggestions="querySearch"
            clearable
            placeholder="可添加未列出类型"
            style="max-width: 160px"
            :disabled="disableEdit"
            @select="handleSelect"
          />
          <el-input
            v-model="standardNumberInputs[index]"
            placeholder="标准号"
            :disabled="disableEdit"
          />
          <el-date-picker
            v-model="executeYear[index]"
            value-format="YYYY"
            type="year"
            placeholder="年份"
            style="min-width: 100px"
            :disabled="disableEdit"
          />
          <el-button
            v-if="index === 0"
            class="shrink-0"
            type="primary"
            :icon="Plus"
            circle
            :disabled="standardInputs.length>=5 || disableEdit"
            @click="handleAdd"
          />
          <el-button
            v-else
            class="shrink-0"
            :icon="Minus"
            circle
            :disabled="disableEdit"
            @click="removeInput(index)"
          />
        </div>
      </el-form-item>
    </el-form>
    <div class="flex mb-12">
      <div class="f-left-babel">
        附件下载
      </div>
      <div>
        <el-button
          :disabled="attachmentUploading || disableEdit"
          type="primary"
          @click="handleAddAttachment"
        >
          <text v-show="!attachmentUploading">
            上传附件
          </text>
          <div
            v-show="attachmentUploading"
            class="flex gap-2 color-white"
          >
            <div
              class="o-loading i-ri:loader-4-fill "
            />
            <text>上传中</text>
          </div>
        </el-button>
        <input
          ref="attachmentFileInput"
          class="hidden"
          type="file"
          @change="handleAttachmentFileChange"
        >
        <div class="mt-4">
          <div
            v-for="item in attachmentList"
            :key="item.url"
            class="flex items-center gap-4"
          >
            <div class="f-file-name o-inline-1">
              <el-link
                type="primary"
                @click="handleDownloadAttachment(item)"
              >
                <el-icon class="pr-2">
                  <Download />
                </el-icon>
                {{ item.name }}
              </el-link>
            </div>
            <div class="f-file-size color-gray">
              {{ getFileSizeStr(item.size) }}
            </div>
            <el-popconfirm
              title="是否确认删除？"
              @confirm="handleDelAttachment(item.url)"
            >
              <template #reference>
                <el-link
                  type="danger"
                  :disabled="disableEdit"
                >
                  删除
                </el-link>
              </template>
            </el-popconfirm>
          </div>
        </div>
      </div>
    </div>
    <div class="flex">
      <div class="f-left-babel">
        商品视频
      </div>
      <div>
        <div
          v-show="videoUploading"
          class="f-video f-video-loading o-flex-c"
        >
          <div class="o-loading i-ri:loader-4-fill color-blue text-6xl" />
        </div>
        <video
          v-show="!videoUploading"
          :src="videoUrl"
          class="f-video"
          controls
        />
        <div class="mb-4 color-gray">
          视频推荐{{ getFileSizeStr(VIDEO_MAX_SIZE) }}以下，避免播放时加载过慢
        </div>
        <div class="flex">
          <el-button
            type="primary"
            :disabled="disableEdit"
            @click="handleAddVideo"
          >
            {{ ruleForm.video?'替换' : '上传' }}
          </el-button>
          <el-button
            v-show="ruleForm.video"
            type="danger"
            :disabled="disableEdit"
            @click="handleDelVideo"
          >
            删除
          </el-button>
        </div>
        <input
          ref="videoFileInput"
          class="hidden"
          type="file"
          accept="video/mp4"
          @change="handleVideoFileChange"
        >
      </div>
    </div>

    <div class="py-4 o-font-default-color">
      商品详情
    </div>
    <div class="color-gray text-sm mb-4">
      说明：上传图片后无需调整大小，手机端预览时会自动调整宽高比例。
    </div>
    <ClientOnly fallback-tag="div">
      <div class="f-editor w-full pb-6">
        <Editor
          v-model="contentHTML"
          :api-key="TINYMCE_KEY"
          :init="editInit"
          :disabled="disableEdit"
          license-key="gpl"
          tinymce-script-src="/tinymce/tinymce.min.js"
        />
      </div>
    </ClientOnly>
    <vue-easy-lightbox
      :imgs="imgList"
      :index="indexRef"
      :visible="visibleRef"
      @hide="onHide"
    />
  </div>
</template>

<style scoped lang="scss">
$w: 146px;

.f-upload-img {
  width: $w;
  height: $w;
}

.f-img-mask{
  background-color: rgba(0, 0, 0, 0.5);
}

.f-img-input {
  @apply rd-2 cursor-pointer;

  width: $w;
  height: $w;
  border: var(--el-border-color-darker) 1px dashed;

  &:hover {
    border-color: var(--el-color-primary);
  }

}

.f-editor {
  //width: 790px;
  height: 600px;
}

.fade-enter-from,
.fade-leave-to {
  transition: all 0.5s ease;
  transform: translateX(-10%);
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  transition: all 0.5s ease;
  transform: translateX(0%);
  opacity: 1;
}

.f-video{
  width: 320px;
  height: 240px;
}

.f-video-loading{
  background-color: rgba(0, 0, 0, 0.1);
}

.f-file-name{
  min-width: 200px;
  max-width: 300px;
}

.f-file-size{
  width: 80px;
}

.f-left-babel{
  @apply pl-19 pr-4 shrink-0;

  color: var(--el-text-color-regular);
}
</style>
