export const miniShopCreateStepStore = defineStore(
  'miniShopCreateStepStore',
  () => {
    const activeStep = ref(0)
    const createType = ref('')
    const createTypeStep2 = ref('')
    const stepBack = () => {
      activeStep.value = activeStep.value - 1
      if (activeStep.value < 0) activeStep.value = 0
      createTypeStep2.value = ''
    }
    const handleStep1 = () => {
      activeStep.value = 1
      createTypeStep2.value = createType.value
      console.log(createTypeStep2.value)
    }

    return { activeStep, createType, createTypeStep2, stepBack, handleStep1 }
  },
)
