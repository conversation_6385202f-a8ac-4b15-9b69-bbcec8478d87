<script setup lang='ts'>
const useStepStore = miniShopCreateStepStore()
const { activeStep } = storeToRefs(useStepStore)
</script>

<template>
  <div class="w-full o-bg-blue-light">
    <AppMenuAndContent>
      <ClientOnly>
        <div class="p-4">
          <el-steps
            :active="activeStep"
            finish-status="success"
            class="mb-4"
          >
            <el-step title="选择新建方式" />
            <el-step title="添加产品信息" />
            <el-step title="完成" />
          </el-steps>
          <ShopCreateStepOne v-if="activeStep === 0" />
          <ShopCreateStepTwo v-if="activeStep === 1" />
          <ShopCreateStepThree v-if="activeStep === 3" />
        </div>
      </ClientOnly>
    </AppMenuAndContent>
  </div>
</template>

<style scoped lang='scss'>

</style>
