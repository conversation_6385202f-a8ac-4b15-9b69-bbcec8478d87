# Nuxt 3 Minimal Starter

Look at the [Nuxt 3 documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

Make sure to install the dependencies:

```bash
# pnpm
pnpm install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# pnpm
pnpm run dev
```

## Production

Build the application for production:
- 先解开[nuxt.config.ts](nuxt.config.ts)
````
    esbuild: {
      // drop: ['console'],
    },
````
```bash
# pnpm
pnpm run build
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

## 字体

https://ecomfe.github.io/fontmin/#app

# 颜色

- 根据给定颜色生成色板，然后放回[primeLocale.ts](config%2FprimeLocale.ts)的MyPreset.primitive.blue中

``` ts
const values1 = palette('#165DFF');
console.log(values1);
```

# 断点

| 断点名称 | 宽度                |
|------|-------------------|
| sm   | min-width: 640px  |
| lg   | min-width: 1024px |
| xl   | min-width: 1280px |
| 2xl  | min-width: 1536px |

# Icon
- 采用unocss方案
- 需要动态渲染的要添加到[uno.config.ts](uno.config.ts)的safelist:[]中。

# 请求封装 useFetchApi
````ts
const formInline = reactive({
  barCode: '',
  goodsName: '',
})
const formBarCode = ref('')
const formGoodsName = ref('')

const { data, status, error, refresh, clear } = await reportGoodsPageApi({
  // barCode: formInline.barCode 不能这样
  // 要下面这样响应式，不用.value，就会根据数据变化而自动refresh
  barCode: formBarCode,
  goodsName: formGoodsName,
  needTotalCount: true,
  orderBy: '',
  orderDirection: OrderDirection.def,
  pageIndex: pageIndex,
  pageSize: pageSize,
  userId: Number(userStore().userId),
})

watch(data, () => {
  if (data.value?.data) {
    list.value = data.value.data
    totalCount.value = data.value.totalCount
  }
}, { immediate: true })

const handleSearch = () => {
  formBarCode.value = formInline.barCode
  formGoodsName.value = formInline.goodsName
  // refresh() 应为是数据变化自动触发刷新，所以这里无需刷新
}
````

# OSS
### 文件命名命名方式：
- /user/用户编码加密（userCodeEncrypt(userStore().userCode)）/各资源目录地址（OssFilePath）/文件名（uuidv4）
- 根目录是用户编码方便统计用户占用资源情况。
````js
// userCodeEncrypt是将userCode（含英文或数字都行）转成更多位数的数字。

// 加密方法
function encrypt(input) {
    let encrypted = '';
    for (let char of input) {
        // 将字符转换为ASCII值并加上一个常数（例如100）
        let ascii = char.charCodeAt(0) + 100;
        encrypted += ascii.toString();
    }
    return encrypted;
}

// 解密方法
function decrypt(encrypted) {
    let decrypted = '';
    // 每个ASCII值占用3位（假设ASCII值范围在100到255）
    for (let i = 0; i < encrypted.length; i += 3) {
        // 提取3位数字并减去100得到原始ASCII值
        let ascii = parseInt(encrypted.substr(i, 3)) - 100;
        decrypted += String.fromCharCode(ascii);
    }
    return decrypted;
}
````
