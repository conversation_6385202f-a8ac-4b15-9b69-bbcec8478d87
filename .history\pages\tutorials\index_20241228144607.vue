<script setup lang="ts">
import { initializeKinesis } from '@amineyarman/kinesis'
import { useArticleListData } from '~/composables/apiArticle'

const articleTypeId = ref(0)

onMounted(() => {
  if (import.meta.client) {
    initializeKinesis()
  }
})

const { data, status, error, refresh, clear } = await useArticleListData({
  articleTypeId: 1,
})

articleTypeId.value = data.value[0].itemList[0].articleTypeId

const articleList = computed(() => {
  return data.value[0].itemList.find(item => item.articleTypeId === articleTypeId.value).articleList
})
const updateArticleTypeId = (id: number) => {
  articleTypeId.value = id
}
</script>

<template>
  <div class="w-full">
    <CarouselPicFilm />
    <div class="max-w-1536px pt-6 pb-10 px-20 mx-auto">
      <div class="flex gap-3">
        <div
          class="f-tile_1 grow-2 rd-2"
          data-kinesistransformer
        >
          <div
            data-kinesistransformer-element
            data-ks-strength="10"
            data-ks-transform="translate"
          >
            <NuxtImg
              class="f-font-standard"
              src="/resource/images/tile/tile_standard.png"
              alt="条码规范"
              width="192"
              height="59"
            />
          </div>
          <div
            data-kinesistransformer-element
            data-ks-strength="30"
            data-ks-transform="translate"
          >
            <NuxtImg
              class="f-img-study"
              src="/resource/images/tile/tile_study.png"
              alt="知识学习"
              width="314"
              height="308"
            />
          </div>
        </div>
        <div
          class="f-tile_2 grow-1 rd-2"
          data-kinesistransformer
        >
          <div
            data-kinesistransformer-element
            data-ks-strength="10"
            data-ks-transform="translate"
          >
            <NuxtImg
              class="f-font-punish"
              src="/resource/images/tile/tile_punish.png"
              alt="违规处罚"
              width="193"
              height="60"
            />
          </div>
          <div
            data-kinesistransformer-element
            data-ks-strength="30"
            data-ks-transform="translate"
          >
            <NuxtImg
              class="f-img-avoid"
              src="/resource/images/tile/tile_avoid.png"
              alt="预防避免"
              width="371"
              height="357"
            />
          </div>
        </div>
      </div>
      <TutorialTab
        v-if="status === 'success'"
        :article-type-id="articleTypeId"
        :data="data"
        :article-list="articleList"
        @update-id="updateArticleTypeId"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
$h: 155px;

.f-tile_1,
.f-tile_2 {
  @apply overflow-hidden relative;

  height: $h;
  background-size: 100% 100%;

  img {
    @apply absolute;
    transform: translateX(-50%);

  }

}

.f-tile_1 {
  background-image: url('/resource/images/tile/tile_bg_stydyStandard.png');
}

.f-tile_2 {
  background-image: url('/resource/images/tile/tile_bg_avoidPunish.png');
}

.f-font-standard {
  top: 16px;
  left: 35%;
}

.f-img-study {
  top: 5px;
  left: 60%;
}

.f-img-avoid {
  top: -20px;
  left: 40%;
}

.f-font-punish {
  top: 16px;
  left: 68%;
}
</style>
